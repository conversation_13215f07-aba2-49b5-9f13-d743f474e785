<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程详情')}">
    <meta charset="UTF-8">
    <title>流程详情</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                流程详情
                <small class="text-muted" th:text="'- ' + ${instance.title}"></small>
            </h1>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <a th:if="${instance.status.name() == 'DRAFT'}" th:href="@{/workflow/approval/{id}(id=${instance.instanceId})}" class="btn btn-primary">
                    <i class="bi bi-send"></i> 提交
                </a>
            </div>
        </div>

        <!-- 成功/错误消息 -->
        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            <span th:text="${message}">操作成功</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span th:text="${error}">操作失败</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>        <!-- 流程信息卡片 -->
        <div class="row mb-4">
            <div class="col-md-12">                <!-- 使用共享的信息卡片片段 -->
                <div th:replace="~{workflow/fragments/workflow-info-card :: info-card(${instance}, ${currentStep}, ${totalSteps}, ${stepLabels}, ${stepComments}, ${stepTimes})}"></div>
            </div>
        </div>

        <!-- 审批记录卡片 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">审批记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" th:if="${!records.isEmpty()}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>操作类型</th>
                                        <th>审批人类型</th>
                                        <th>操作人</th>
                                        <th>操作时间</th>
                                        <th>审批意见</th>
                                        <th>附件</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="record : ${records}">
                                        <td>
                                            <span th:if="${record.action.name() == 'SUBMIT'}" class="badge bg-info">提交</span>
                                            <span th:if="${record.action.name() == 'APPROVE'}" class="badge bg-success">同意</span>
                                            <span th:if="${record.action.name() == 'REJECT'}" class="badge bg-danger">拒绝</span>
                                            <span th:if="${record.action.name() == 'TRANSFER'}" class="badge bg-warning">转交</span>
                                            <span th:if="${record.action.name() == 'WITHDRAW'}" class="badge bg-secondary">撤回</span>
                                            <span th:if="${record.action.name() == 'TERMINATE'}" class="badge bg-dark">终止</span>
                                            <span th:if="${record.action.name() == 'COMMENT'}" class="badge bg-light text-dark">评论</span>
                                        </td>                                        <td>
                                            <span th:if="${record.step != null && record.step.approverType != null}">
                                                <span th:if="${record.step.approverType.name() == 'FIXED_USER'}" class="badge bg-secondary">固定用户</span>
                                                <span th:if="${record.step.approverType.name() == 'ROLE'}" class="badge bg-info">角色</span>
                                                <span th:if="${record.step.approverType.name() == 'DEPARTMENT_HEAD'}" class="badge bg-primary">部门主管</span>
                                                <span th:if="${record.step.approverType.name() == 'DYNAMIC'}" class="badge bg-warning">动态指定</span>
                                            </span>
                                            <!-- 当step信息不可用时的fallback逻辑 -->
                                            <span th:unless="${record.step != null && record.step.approverType != null}">
                                                <!-- 提交和撤回操作固定为发起人操作 -->
                                                <span th:if="${record.action.name() == 'SUBMIT' || record.action.name() == 'WITHDRAW'}" class="badge bg-secondary">固定用户</span>
                                                <!-- 终止操作固定为管理员操作 -->
                                                <span th:if="${record.action.name() == 'TERMINATE'}" class="badge bg-dark">固定用户</span>
                                                <!-- 转交操作属于动态指定 -->
                                                <span th:if="${record.action.name() == 'TRANSFER'}" class="badge bg-warning">动态指定</span>
                                                <!-- 评论操作固定为当前审批人 -->
                                                <span th:if="${record.action.name() == 'COMMENT'}" class="badge bg-secondary">固定用户</span>
                                                <!-- 审批和拒绝操作：当步骤信息不可用时，显示动态指定 -->
                                                <span th:if="${record.action.name() == 'APPROVE' || record.action.name() == 'REJECT'}" class="badge bg-warning">动态指定</span>
                                            </span>
                                        </td>
                                        <td th:text="${record.approver}">操作人</td>
                                        <td th:text="${record.createdDateTime != null ? #temporals.format(record.createdDateTime, 'yyyy-MM-dd HH:mm:ss') : '-'}">操作时间</td>
                                        <td th:text="${record.comment ?: '-'}">审批意见</td>                                        <td>
                                            <div th:if="${record.attachments != null && !record.attachments.isEmpty()}" 
                                                 class="d-flex flex-column gap-1">
                                                <div th:each="attachment, status : ${record.attachments.split(';')}">
                                                    <a th:href="@{/workflow/instances/download/attachment(path=${attachment})}" target="_blank" 
                                                       class="btn btn-sm btn-outline-primary d-flex align-items-center">
                                                        <i class="bi bi-download me-1"></i> 
                                                        <span th:text="'附件' + ${status.count}">附件</span>
                                                    </a>
                                                </div>
                                            </div>
                                            <span th:unless="${record.attachments != null && !record.attachments.isEmpty()}">-</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${records.isEmpty()}">
                            <p class="text-muted mb-0">暂无审批记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script th:src="@{/js/workflow/instance-form.js}"></script>
</body>
</html>
