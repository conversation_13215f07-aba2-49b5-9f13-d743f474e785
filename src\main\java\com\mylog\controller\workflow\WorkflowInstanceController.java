package com.mylog.controller.workflow;

import com.mylog.controller.BaseController;

import com.mylog.model.ProjectTask;
import com.mylog.model.user.User;
import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import com.mylog.model.workflow.WorkflowStep;
import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import java.util.*;

/**
 * 流程实例控制器
 */
@Controller
@RequestMapping("/workflow/instances")
public class WorkflowInstanceController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowInstanceController.class);
    @Autowired
    private WorkflowInstanceService instanceService;

    @Autowired
    private com.mylog.repository.workflow.WorkflowInstanceRepository instanceRepository;

    @Autowired
    private WorkflowTemplateService templateService;

    @Autowired
    private ApprovalRecordService recordService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private WorkflowStepService stepService;

    @Autowired
    private OptionsService optionsService;

    @Value("${mylog.data.path:data}")
    private String dataPath;

    /**
     * 显示流程实例列表
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public String listInstances(
            Model model,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "status", required = false) WorkflowStatus status,
            @RequestParam(value = "initiator", required = false) String initiator,
            @RequestParam(value = "templateId", required = false) String templateIdStr,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "businessId", required = false) String businessIdStr) {

        try {
            // 创建分页请求
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate"));
            // 记录搜索参数
            logger.info("流程实例搜索参数 - 关键词: {}, 状态: {}, 发起人: {}, 模板ID: {}, 业务类型: {}, 业务ID: {}",
                    keyword, status, initiator, templateIdStr, businessType, businessIdStr);

            // 转换模板ID参数
            Long templateId = null;
            if (templateIdStr != null && !templateIdStr.trim().isEmpty()) {
                try {
                    templateId = Long.parseLong(templateIdStr);
                    logger.info("转换后的模板ID: {}", templateId);
                } catch (NumberFormatException e) {
                    logger.warn("无效的模板ID格式: {}", templateIdStr);
                }
            }
            // 转换业务ID参数
            Long businessId = null;
            if (businessIdStr != null && !businessIdStr.trim().isEmpty()) {
                try {
                    businessId = Long.parseLong(businessIdStr);
                    logger.info("转换后的业务ID: {}", businessId);
                } catch (NumberFormatException e) {
                    logger.warn("无效的业务ID格式: {}", businessIdStr);
                }
            } // 获取实例列表
            Page<WorkflowInstance> instancePage = instanceService.searchInstances(
                    keyword, status, initiator, templateId, businessType, businessIdStr, pageable);
            // 记录搜索结果
            logger.info("流程实例搜索结果 - 总条数: {}, 当前页记录数: {}",
                    instancePage.getTotalElements(), instancePage.getNumberOfElements());

            // 添加更详细的日志以便排查问题
            if (instancePage.getTotalElements() == 0) {
                logger.info("搜索结果为空，检查数据库中是否存在匹配的数据");
                // 添加一些不带过滤条件的查询，看看数据库中是否有数据
                long totalCount = instanceRepository.count();
                logger.info("数据库中流程实例总数: {}", totalCount);

                if (totalCount > 0) {
                    // 如果有数据，但搜索结果为空，可能是搜索条件太严格
                    logger.info("数据库中有数据，但搜索结果为空，可能是搜索条件过滤掉了所有数据");

                    // 尝试只使用templateId查询
                    if (templateId != null) {
                        long countByTemplate = instanceRepository.findByTemplateTemplateId(templateId).size();
                        logger.info("仅按模板ID {} 查询的结果数: {}", templateId, countByTemplate);
                    }

                    // 尝试只使用status查询
                    if (status != null) {
                        long countByStatus = instanceRepository.countByStatus(status);
                        logger.info("仅按状态 {} 查询的结果数: {}", status, countByStatus);
                    }

                    // 尝试查看initiator搜索情况
                    if (initiator != null && !initiator.isEmpty()) {
                        logger.info("当前搜索的发起人值: '{}'", initiator);
                        // 使用like查询手动测试
                        String testQuery = "%" + initiator + "%";
                        logger.info("尝试使用模糊匹配模式 '{}' 查询发起人", testQuery);
                    }
                }
            }

            // 获取模板列表（用于筛选）
            List<WorkflowTemplate> templates = templateService.findAllTemplates();

            // 计算每个实例的当前步骤 - 添加这个Map来存储步骤信息
            Map<Long, Integer> instanceSteps = new HashMap<>();

            // 遍历实例，计算当前步骤
            for (WorkflowInstance instance : instancePage.getContent()) {
                if (instance.getInstanceId() != null) {
                    try {
                        // 获取审批记录
                        List<ApprovalRecord> records = recordService.findRecordsByInstanceId(instance.getInstanceId());

                        // 计算当前步骤
                        int currentStep = 0;

                        if (instance.getStatus() == WorkflowStatus.DRAFT) {
                            currentStep = 0;
                        } else {
                            // 计算已经完成的步骤数（提交 + 审批记录中的审批/拒绝操作）
                            boolean hasSubmit = false;
                            int approvalSteps = 0;

                            for (ApprovalRecord record : records) {
                                if (record.getAction() == ApprovalRecord.ApprovalAction.SUBMIT) {
                                    hasSubmit = true;
                                } else if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                                        || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                                    approvalSteps++;
                                }
                            }

                            // 如果已经提交，当前步骤至少是1
                            currentStep = hasSubmit ? 1 : 0;
                            // 加上审批步骤数
                            currentStep += approvalSteps;
                        }

                        // 保存到Map中
                        instanceSteps.put(instance.getInstanceId(), currentStep);
                        logger.debug("实例ID {} 当前步骤: {}", instance.getInstanceId(), currentStep);
                    } catch (Exception e) {
                        logger.error("计算实例 {} 的步骤信息时出错: {}", instance.getInstanceId(), e.getMessage());
                        // 默认为0步
                        instanceSteps.put(instance.getInstanceId(), 0);
                    }
                }
            } // 添加到模型
            model.addAttribute("instancePage", instancePage);
            model.addAttribute("templates", templates);
            model.addAttribute("statuses", WorkflowStatus.values());
            model.addAttribute("keyword", keyword);
            model.addAttribute("status", status);
            model.addAttribute("initiator", initiator);
            model.addAttribute("personnelList", optionsService.getPersonnel()); // 添加人员列表用于发起人选择
            model.addAttribute("templateId", templateIdStr); // 使用原始字符串值
            model.addAttribute("businessType", businessType);
            model.addAttribute("businessId", businessIdStr); // 使用原始字符串值
            model.addAttribute("activeMenu", "workflow");
            // 添加步骤信息到模型
            model.addAttribute("instanceSteps", instanceSteps);

            return "workflow/instances/index";
        } catch (Exception e) {
            logger.error("加载流程实例列表时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 显示我的流程列表
     */
    @GetMapping("/my")
    @PreAuthorize("isAuthenticated()")
    public String myInstances(
            Model model,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "status", required = false) WorkflowStatus status) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 创建分页请求
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate"));

            // 获取实例列表
            Page<WorkflowInstance> instancePage = instanceService.searchInstances(
                    null, status, currentUsername, null, null, null, pageable);

            // 计算每个实例的当前步骤 - 添加这个Map来存储步骤信息
            Map<Long, Integer> instanceSteps = new HashMap<>();

            // 遍历实例，计算当前步骤
            for (WorkflowInstance instance : instancePage.getContent()) {
                if (instance.getInstanceId() != null) {
                    try {
                        // 获取审批记录
                        List<ApprovalRecord> records = recordService.findRecordsByInstanceId(instance.getInstanceId());

                        // 计算当前步骤
                        int currentStep = 0;

                        if (instance.getStatus() == WorkflowStatus.DRAFT) {
                            currentStep = 0;
                        } else {
                            // 计算已经完成的步骤数（提交 + 审批记录中的审批/拒绝操作）
                            boolean hasSubmit = false;
                            int approvalSteps = 0;

                            for (ApprovalRecord record : records) {
                                if (record.getAction() == ApprovalRecord.ApprovalAction.SUBMIT) {
                                    hasSubmit = true;
                                } else if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                                        || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                                    approvalSteps++;
                                }
                            }

                            // 如果已经提交，当前步骤至少是1
                            currentStep = hasSubmit ? 1 : 0;
                            // 加上审批步骤数
                            currentStep += approvalSteps;
                        }

                        // 保存到Map中
                        instanceSteps.put(instance.getInstanceId(), currentStep);
                        logger.debug("实例ID {} 当前步骤: {}", instance.getInstanceId(), currentStep);
                    } catch (Exception e) {
                        logger.error("计算实例 {} 的步骤信息时出错: {}", instance.getInstanceId(), e.getMessage());
                        // 默认为0步
                        instanceSteps.put(instance.getInstanceId(), 0);
                    }
                }
            }

            // 添加到模型
            model.addAttribute("instancePage", instancePage);
            model.addAttribute("statuses", WorkflowStatus.values());
            model.addAttribute("status", status);
            model.addAttribute("activeMenu", "myworkflow");
            // 添加步骤信息到模型
            model.addAttribute("instanceSteps", instanceSteps);

            return "workflow/instances/my-instances";
        } catch (Exception e) {
            logger.error("加载我的流程列表时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 显示待审批任务列表
     */
    @GetMapping("/todo")
    public String todoTasks(
            Model model,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 创建分页请求
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate"));

            // 获取待审批任务列表
            Page<WorkflowInstance> todoPage = instanceService.findTodoTasks(currentUsername, pageable);

            // 计算每个实例的当前步骤 - 添加这个Map来存储步骤信息
            Map<Long, Integer> instanceSteps = new HashMap<>();

            // 遍历实例，计算当前步骤
            for (WorkflowInstance instance : todoPage.getContent()) {
                if (instance.getInstanceId() != null) {
                    try {
                        // 获取审批记录
                        List<ApprovalRecord> records = recordService.findRecordsByInstanceId(instance.getInstanceId());

                        // 计算当前步骤
                        int currentStep = 0;

                        if (instance.getStatus() == WorkflowStatus.DRAFT) {
                            currentStep = 0;
                        } else {
                            // 计算已经完成的步骤数（提交 + 审批记录中的审批/拒绝操作）
                            boolean hasSubmit = false;
                            int approvalSteps = 0;

                            for (ApprovalRecord record : records) {
                                if (record.getAction() == ApprovalRecord.ApprovalAction.SUBMIT) {
                                    hasSubmit = true;
                                } else if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                                        || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                                    approvalSteps++;
                                }
                            }

                            // 如果已经提交，当前步骤至少是1
                            currentStep = hasSubmit ? 1 : 0;
                            // 加上审批步骤数
                            currentStep += approvalSteps;
                        }

                        // 保存到Map中
                        instanceSteps.put(instance.getInstanceId(), currentStep);
                        logger.debug("实例ID {} 当前步骤: {}", instance.getInstanceId(), currentStep);
                    } catch (Exception e) {
                        logger.error("计算实例 {} 的步骤信息时出错: {}", instance.getInstanceId(), e.getMessage());
                        // 默认为0步
                        instanceSteps.put(instance.getInstanceId(), 0);
                    }
                }
            }

            // 添加到模型
            model.addAttribute("todoPage", todoPage);
            model.addAttribute("activeMenu", "todotasks");
            // 添加步骤信息到模型
            model.addAttribute("instanceSteps", instanceSteps);

            return "workflow/instances/todo-tasks";
        } catch (Exception e) {
            logger.error("加载待审批任务列表时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "加载待审批任务失败: " + e.getMessage());
            return "redirect:/workflow/my";
        }
    }

    /**
     * 显示已审批任务列表
     */
    @GetMapping("/done")
    public String doneTasks(
            Model model,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 创建分页请求
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate"));

            // 获取已审批任务列表
            Page<WorkflowInstance> donePage = instanceService.findDoneTasks(currentUsername, pageable);

            // 计算每个实例的当前步骤 - 添加这个Map来存储步骤信息
            Map<Long, Integer> instanceSteps = new HashMap<>();

            // 遍历实例，计算当前步骤
            for (WorkflowInstance instance : donePage.getContent()) {
                if (instance.getInstanceId() != null) {
                    try {
                        // 获取审批记录
                        List<ApprovalRecord> records = recordService.findRecordsByInstanceId(instance.getInstanceId());

                        // 计算当前步骤
                        int currentStep = 0;

                        if (instance.getStatus() == WorkflowStatus.DRAFT) {
                            currentStep = 0;
                        } else {
                            // 计算已经完成的步骤数（提交 + 审批记录中的审批/拒绝操作）
                            boolean hasSubmit = false;
                            int approvalSteps = 0;

                            for (ApprovalRecord record : records) {
                                if (record.getAction() == ApprovalRecord.ApprovalAction.SUBMIT) {
                                    hasSubmit = true;
                                } else if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                                        || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                                    approvalSteps++;
                                }
                            }

                            // 如果已经提交，当前步骤至少是1
                            currentStep = hasSubmit ? 1 : 0;
                            // 加上审批步骤数
                            currentStep += approvalSteps;
                        }

                        // 保存到Map中
                        instanceSteps.put(instance.getInstanceId(), currentStep);
                        logger.debug("实例ID {} 当前步骤: {}", instance.getInstanceId(), currentStep);
                    } catch (Exception e) {
                        logger.error("计算实例 {} 的步骤信息时出错: {}", instance.getInstanceId(), e.getMessage());
                        // 默认为0步
                        instanceSteps.put(instance.getInstanceId(), 0);
                    }
                }
            }

            // 添加到模型
            model.addAttribute("donePage", donePage);
            model.addAttribute("activeMenu", "donetasks");
            // 添加步骤信息到模型
            model.addAttribute("instanceSteps", instanceSteps);

            return "workflow/instances/done-tasks";
        } catch (Exception e) {
            logger.error("加载已审批任务列表时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 显示流程实例详情
     */
    @GetMapping("/{id}")
    public String viewInstance(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        try {
            // 记录请求日志，便于调试
            logger.info("查看流程实例详情，ID: {}", id);

            Optional<WorkflowInstance> instanceOpt = instanceService.findInstanceById(id);
            if (instanceOpt.isPresent()) {
                WorkflowInstance instance = instanceOpt.get();
                logger.info("找到流程实例: {}, 状态: {}", instance.getTitle(), instance.getStatus());

                // 检查模板是否存在
                if (instance.getTemplate() == null) {
                    logger.error("流程实例 ID {} 的模板为空", id);
                    redirectAttributes.addFlashAttribute("error", "流程模板信息缺失");
                    return "redirect:/workflow/instances";
                }

                // 获取审批记录
                List<ApprovalRecord> records = recordService.findRecordsByInstanceId(id);
                logger.info("找到审批记录数量: {}", records.size());

                // 计算当前步骤和总步骤数
                int currentStep = 0;
                int totalSteps = 0;

                // 使用安全的方式从实例对象中读取总步骤数
                try {
                    totalSteps = instance.getStepCount();
                    logger.info("从数据库读取的步骤总数: {}", totalSteps);
                } catch (Exception e) {
                    logger.warn("计算总步骤数时出错: {}", e.getMessage());
                    totalSteps = 0;
                }

                // 根据流程状态和审批记录计算当前步骤
                if (instance.getStatus() == WorkflowStatus.DRAFT) {
                    currentStep = 0;
                } else {
                    // 计算已经完成的步骤数（提交 + 审批记录中的审批/拒绝操作）
                    boolean hasSubmit = false;
                    int approvalSteps = 0;

                    for (ApprovalRecord record : records) {
                        try {
                            if (record.getAction() == ApprovalRecord.ApprovalAction.SUBMIT) {
                                hasSubmit = true;
                            } else if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                                    || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                                approvalSteps++;
                            }
                        } catch (Exception e) {
                            logger.warn("处理审批记录时出错: {}", e.getMessage());
                        }
                    }

                    // 如果已经提交，当前步骤至少是1
                    currentStep = hasSubmit ? 1 : 0;
                    // 加上审批步骤数
                    currentStep += approvalSteps;
                }

                logger.info("计算的当前步骤: {}, 总步骤数: {} (包含提交步骤)",
                        currentStep,
                        totalSteps);

                // 创建步骤标签映射，第1步是提交步骤，后续是审批步骤
                Map<Integer, String> stepLabels = new HashMap<>();

                // 第1步是提交步骤，标签为"提交"
                stepLabels.put(1, "提交: " + instance.getInitiator());

                // 获取完整步骤列表
                List<WorkflowStep> allSteps = new ArrayList<>();
                if (instance.getTemplate() != null) {
                    allSteps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
                    allSteps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));
                }

                // 从审批记录中提取步骤审批人信息
                for (ApprovalRecord record : records) {
                    if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                            || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                        // 找到对应的步骤序号
                        int stepIndex = 2; // 默认为1
                        if (record.getStep() != null) {
                            for (int i = 0; i < allSteps.size(); i++) {
                                if (allSteps.get(i).getStepId().equals(record.getStep().getStepId())) {
                                    stepIndex = i + 2; // +2是因为第1步是提交步骤
                                    break;
                                }
                            }
                        }

                        // 将审批人添加到步骤标签中
                        stepLabels.put(stepIndex,
                                record.getStep() != null ? record.getStep().getStepName() + ": " + record.getApprover()
                                        : "审批: " + record.getApprover());
                    }
                }

                // 对于还未完成的步骤，使用步骤名称作为标签
                for (int i = 0; i < allSteps.size(); i++) {
                    int stepIndex = i + 2; // +2是因为第1步是提交步骤
                    if (!stepLabels.containsKey(stepIndex) && stepIndex <= totalSteps) {
                        WorkflowStep step = allSteps.get(i);
                        String approverInfo = "";

                        if (step.getStepId().equals(instance.getCurrentStepId())) {
                            // 如果步骤是实例中的当前步骤，则从实例中读取当前审批人
                            approverInfo = ": "
                                    + (instance.getCurrentApprover() != null ? instance.getCurrentApprover() : "待定");
                        } else {
                            // 如果步骤不是实例中的当前步骤，则从步骤配置中读取审批人
                            approverInfo = ": "
                                    + ((step.getApproverConfig() != null && !step.getApproverConfig().trim().isEmpty())
                                            ? step.getApproverConfig()
                                            : "待定");
                        }                        stepLabels.put(stepIndex, step.getStepName() + approverInfo);
                    }
                }                // 创建步骤时间映射
                Map<Integer, String> stepTimes = new HashMap<>();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");

                // 创建步骤意见映射
                Map<Integer, String> stepComments = new HashMap<>();

                // 添加提交时间和意见（第1步）
                if (instance.getSubmittedDateTime() != null) {
                    stepTimes.put(1, instance.getSubmittedDateTime().format(formatter));
                }
                // 查找提交记录的意见
                for (ApprovalRecord record : records) {
                    if (record.getAction() == ApprovalRecord.ApprovalAction.SUBMIT) {
                        if (record.getComment() != null && !record.getComment().trim().isEmpty()) {
                            stepComments.put(1, record.getComment());
                        }
                        break;
                    }
                }

                // 添加审批时间和意见（第2步及以后）
                for (ApprovalRecord record : records) {
                    if (record.getAction() == ApprovalRecord.ApprovalAction.APPROVE
                            || record.getAction() == ApprovalRecord.ApprovalAction.REJECT) {
                        // 找到对应的步骤序号
                        int stepIndex = 2; // 默认为2
                        if (record.getStep() != null) {
                            for (int i = 0; i < allSteps.size(); i++) {
                                if (allSteps.get(i).getStepId().equals(record.getStep().getStepId())) {
                                    stepIndex = i + 2; // +2是因为第1步是提交步骤
                                    break;
                                }
                            }
                        }

                        // 添加审批时间
                        if (record.getCreatedDateTime() != null) {
                            stepTimes.put(stepIndex, record.getCreatedDateTime().format(formatter));
                        }

                        // 添加审批意见
                        if (record.getComment() != null && !record.getComment().trim().isEmpty()) {
                            stepComments.put(stepIndex, record.getComment());
                        }
                    }
                }

                // 添加到模型
                model.addAttribute("instance", instance);
                model.addAttribute("records", records);
                model.addAttribute("currentStep", currentStep);
                model.addAttribute("totalSteps", totalSteps);
                model.addAttribute("stepLabels", stepLabels);
                model.addAttribute("stepTimes", stepTimes);
                model.addAttribute("stepComments", stepComments);
                model.addAttribute("activeMenu", "workflow");

                return "workflow/instances/view";
            } else {
                logger.warn("流程实例不存在，ID: {}", id);
                redirectAttributes.addFlashAttribute("error", "流程实例不存在");
                return "redirect:/workflow/instances";
            }
        } catch (Exception e) {
            logger.error("查看流程实例详情时出错，ID: {}, 错误: {}", id, e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "查看流程实例详情失败: " + e.getMessage());
            return "redirect:/workflow/instances";
        }
    }

    /**
     * 显示创建流程实例表单
     */
    @GetMapping("/create")
    public String showCreateForm(
            @RequestParam(value = "templateId", required = false) Long templateId,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "businessId", required = false) Long businessId,
            @RequestParam(value = "title", required = false) String title,
            Model model) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 创建新实例
            WorkflowInstance instance = new WorkflowInstance();
            instance.setInitiator(currentUsername);
            instance.setBusinessType(businessType);
            instance.setBusinessId(businessId);

            // 如果提供了标题，设置到实例中
            if (title != null && !title.isEmpty()) {
                instance.setTitle(title);
            }

            // 如果是任务审批，尝试根据项目类型自动选择对应的流程模板
            if ("任务".equals(businessType) && templateId == null && businessId != null) {
                // 根据任务所属项目类型选择不同的审批模板
                String projectType = null;
                try {
                    // 获取任务信息
                    Optional<ProjectTask> taskOpt = taskService.findTaskById(businessId);
                    if (taskOpt.isPresent()) {
                        ProjectTask task = taskOpt.get();
                        // 获取项目信息
                        if (task.getProject() != null) {
                            projectType = task.getProject().getProjectType();
                            logger.info("任务 {} 所属项目类型: {}", businessId, projectType);
                        } else {
                            logger.warn("任务 {} 没有关联的项目信息", businessId);
                        }
                    } else {
                        logger.warn("未找到任务ID: {}", businessId);
                    }
                } catch (Exception e) {
                    logger.error("获取任务项目类型时出错: {}", e.getMessage(), e);
                }

                // 根据项目类型选择对应的审批模板
                String templateNameKeyword = "任务审批"; // 默认模板关键词
                if (projectType != null) {
                    switch (projectType) {
                        case "订单":
                            templateNameKeyword = "订单";
                            break;
                        case "程序":
                            templateNameKeyword = "程序";
                            break;
                        case "资料":
                            templateNameKeyword = "资料";
                            break;
                        case "事务":
                            templateNameKeyword = "事务";
                            break;
                        default:
                            templateNameKeyword = "任务审批"; // 其他类型使用默认模板
                            break;
                    }
                    logger.info("根据项目类型 {} 选择模板关键词: {}", projectType, templateNameKeyword);
                }

                List<WorkflowTemplate> templates = templateService.findAllTemplates();
                for (WorkflowTemplate template : templates) {
                    if (template.getTemplateName() != null && template.getTemplateName().contains(templateNameKeyword)) {
                        templateId = template.getTemplateId();
                        logger.info("自动选择了审批模板: {} (ID: {})", template.getTemplateName(), templateId);
                        break;
                    }
                }
            } // 如果指定了模板ID，加载模板
            if (templateId != null) {
                Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(templateId);
                if (templateOpt.isPresent()) {
                    WorkflowTemplate template = templateOpt.get();
                    // 如果模板已禁用，则不使用
                    if (template.getEnabled() != null && template.getEnabled()) {
                        instance.setTemplate(template);
                    } else {
                        logger.warn("尝试使用已禁用的模板: {}", template.getTemplateName());
                    }
                }
            }

            // 获取可用模板列表，只获取启用的模板
            List<WorkflowTemplate> templates = templateService.findByEnabled(true);

            // 获取人员列表并添加到模型中
            List<String> personnel = optionsService.getPersonnel();
            logger.info("加载人员列表: 共 {} 人", personnel.size());

            // 添加到模型
            model.addAttribute("instance", instance);
            model.addAttribute("templates", templates);
            model.addAttribute("personnel", personnel);
            model.addAttribute("activeMenu", "workflow");

            return "workflow/instances/form";
        } catch (Exception e) {
            logger.error("显示创建流程实例表单时出错: {}", e.getMessage(), e);
            // 不要直接抛出异常，而是添加错误消息并返回错误视图
            model.addAttribute("error", "显示创建流程实例表单时出错: " + e.getMessage());
            model.addAttribute("activeMenu", "workflow");
            return "error/general";
        }
    }

    /**
     * 自动创建任务审批流程实例并跳转到提交页面
     */    @GetMapping("/auto-create-for-task")
    public String autoCreateForTask(
            @RequestParam("businessId") Long businessId,
            @RequestParam("title") String title,
            @RequestParam(value = "remarks", required = false) String remarks,
            @RequestParam(value = "taskCompletionStatus", required = false) String taskCompletionStatus,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户

            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 创建新实例
            WorkflowInstance instance = new WorkflowInstance();
            instance.setInitiator(currentUsername);
            instance.setBusinessType("任务");
            instance.setBusinessId(businessId);
            instance.setTitle(title); // 查找任务审批流程模板，只选择启用状态的模板
              // 设置任务提交备注到 remark4
            if (remarks != null && !remarks.trim().isEmpty()) {
                instance.setRemark4(remarks);
                logger.info("已设置任务 {} 的提交备注到工作流实例: {}", businessId, remarks);
            } else {
                instance.setRemark4("");
                logger.info("任务 {} 没有提交备注", businessId);
            }
            
            Long templateId = null;
            List<WorkflowTemplate> templates = templateService.findByEnabled(true);

            // 根据任务所属项目类型选择不同的审批模板
            String projectType = null;
            try {
                // 获取任务信息
                Optional<ProjectTask> taskOpt = taskService.findTaskById(businessId);
                if (taskOpt.isPresent()) {
                    ProjectTask task = taskOpt.get();
                    // 获取项目信息
                    if (task.getProject() != null) {
                        projectType = task.getProject().getProjectType();
                        logger.info("任务 {} 所属项目类型: {}", businessId, projectType);
                    } else {
                        logger.warn("任务 {} 没有关联的项目信息", businessId);
                    }
                } else {
                    logger.warn("未找到任务ID: {}", businessId);
                }
            } catch (Exception e) {
                logger.error("获取任务项目类型时出错: {}", e.getMessage(), e);
            }

            // 根据项目类型选择对应的审批模板
            String templateNameKeyword = "任务审批"; // 默认模板关键词
            if (projectType != null) {
                switch (projectType) {
                    case "订单":
                        templateNameKeyword = "订单";
                        break;
                    case "程序":
                        templateNameKeyword = "程序";
                        break;
                    case "资料":
                        templateNameKeyword = "资料";
                        break;
                    case "事务":
                        templateNameKeyword = "事务";
                        break;
                    default:
                        templateNameKeyword = "任务审批"; // 其他类型使用默认模板
                        break;
                }
                logger.info("根据项目类型 {} 选择模板关键词: {}", projectType, templateNameKeyword);
            }

            for (WorkflowTemplate template : templates) {
                if (template.getTemplateName() != null && template.getTemplateName().contains(templateNameKeyword)) {
                    templateId = template.getTemplateId();
                    instance.setTemplate(template);
                    // 将模板描述复制到实例的流程说明中
                    if (template.getDescription() != null && !template.getDescription().isEmpty()) {
                        instance.setDescription(template.getDescription());
                    }
                    logger.info("选择了审批模板: {} (ID: {})", template.getTemplateName(), templateId);
                    break;
                }
            }

            if (templateId == null) {
                // templateId为空时，重置任务审批状态为不需要审批
                throw new Exception("未找到任务审批流程模板");

            }

            // 创建实例
            WorkflowInstance savedInstance = instanceService.createInstance(instance);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logCreate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "创建流程实例: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        savedInstance.getInstanceId(),
                        getAccessType());
            }

            // 更新任务审批状态为"审批中"，并关联流程实例ID
            taskService.updateTaskApprovalStatus(businessId, 1, savedInstance.getInstanceId(), currentUsername);
            logger.info("已更新任务 {} 的审批状态为审批中，关联流程实例ID: {}", businessId, savedInstance.getInstanceId());

            // 直接跳转到流程提交页面
            return "redirect:/workflow/approval/" + savedInstance.getInstanceId();

        } catch (Exception e) {
            logger.error("自动创建任务审批流程实例时出错: {}", e.getMessage(), e); // 流程创建失败时，重置任务审批状态为不需要审批
            try {
                logger.info("正在重置任务 {} 的审批状态为不需要审批", businessId);
                taskService.updateTaskApprovalStatus(businessId, 0, null, null);
                logger.info("已成功重置任务 {} 的审批状态", businessId);
            } catch (Exception ex) {
                logger.error("重置任务审批状态时发生错误: {}", ex.getMessage(), ex);
                // 即使重置失败，继续返回错误消息
            }

            redirectAttributes.addFlashAttribute("error", "创建审批流程失败: " + e.getMessage());
            return "redirect:/tasks/" + businessId;
        }
    }

    /**
     * 保存流程实例
     */    @PostMapping("/save")
    public String saveInstance(
            WorkflowInstance instance,
            @RequestParam("templateId") Long templateId,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "businessId", required = false) Long businessId,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "startTimeFormatted", required = false) String startTimeFormatted,
            @RequestParam(value = "endTimeFormatted", required = false) String endTimeFormatted,
            @RequestParam(value = "startLocation", required = false) String startLocation,
            @RequestParam(value = "endLocation", required = false) String endLocation,
            @RequestParam(value = "remark1", required = false) String remark1,
            @RequestParam(value = "remark2", required = false) String remark2,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取模板
            Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(templateId);
            if (!templateOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程模板不存在");
                return "redirect:/workflow/instances/create";
            }

            // 设置模板和发起人
            instance.setTemplate(templateOpt.get());
            instance.setInitiator(currentUsername);

            // 如果是从任务提交跳转过来的，设置业务类型和业务ID
            if (businessType != null && businessId != null) {
                instance.setBusinessType(businessType);
                instance.setBusinessId(businessId);
            }            // 处理日期时间字段，确保符合yyyy-MM-dd HH:mm:ss格式
            // 优先使用前端格式化好的日期时间字段
            if (startTimeFormatted != null && !startTimeFormatted.isEmpty()) {
                // 前端已格式化为yyyy-MM-dd HH:mm:ss
                instance.setStartTime(startTimeFormatted);
            } else if (startTime != null && !startTime.isEmpty()) {
                // 备用处理：如果前端格式化失败，后端进行格式化
                if (startTime.contains("T")) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.parse(startTime);
                        instance.setStartDateTime(dateTime); // 使用setter方法确保格式一致
                    } catch (DateTimeParseException e) {
                        logger.warn("无法解析开始时间: {}", startTime, e);
                        instance.setStartTime(null);
                    }
                } else {
                    instance.setStartTime(startTime);
                }
            }
            
            // 同样处理结束时间
            if (endTimeFormatted != null && !endTimeFormatted.isEmpty()) {
                // 前端已格式化为yyyy-MM-dd HH:mm:ss
                instance.setEndTime(endTimeFormatted);
            } else if (endTime != null && !endTime.isEmpty()) {
                // 备用处理：如果前端格式化失败，后端进行格式化
                if (endTime.contains("T")) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.parse(endTime);
                        instance.setEndDateTime(dateTime); // 使用setter方法确保格式一致
                    } catch (DateTimeParseException e) {
                        logger.warn("无法解析结束时间: {}", endTime, e);
                        instance.setEndTime(null);
                    }
                } else {
                    instance.setEndTime(endTime);
                }
            }
            
            // 设置其他字段
            instance.setStartLocation(startLocation);
            instance.setEndLocation(endLocation);
            instance.setRemark1(remark1);
            instance.setRemark2(remark2);

            // 创建实例
            WorkflowInstance savedInstance = instanceService.createInstance(instance);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logCreate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "创建流程实例: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        savedInstance.getInstanceId(),
                        getAccessType());
            }

            // 如果是任务审批流程，更新任务的审批状态和关联的流程实例ID
            if ("任务".equals(businessType) && businessId != null) {                try {
                    // 更新任务审批状态为"审批中"，并关联流程实例ID
                    taskService.updateTaskApprovalStatus(businessId, 1, savedInstance.getInstanceId(), currentUsername);
                    logger.info("已更新任务 {} 的审批状态为审批中，关联流程实例ID: {}", businessId, savedInstance.getInstanceId());
                } catch (Exception e) {
                    logger.error("更新任务审批状态时出错: {}", e.getMessage(), e);
                    // 继续执行，不影响流程创建
                }
            }

            redirectAttributes.addFlashAttribute("message", "流程实例创建成功");
            return "redirect:/workflow/instances/" + savedInstance.getInstanceId();
        } catch (Exception e) {
            logger.error("保存流程实例时出错: {}", e.getMessage(), e);

            // 如果是任务审批流程，当创建失败时重置任务的审批状态
            if ("任务".equals(businessType) && businessId != null) {                try {
                    logger.info("正在重置任务 {} 的审批状态为不需要审批", businessId);
                    taskService.updateTaskApprovalStatus(businessId, 0, null, null);
                    logger.info("已成功重置任务 {} 的审批状态", businessId);
                } catch (Exception ex) {
                    logger.error("重置任务审批状态时发生错误: {}", ex.getMessage(), ex);
                    // 即使重置失败，继续返回错误消息
                }
            }

            redirectAttributes.addFlashAttribute("error", "保存流程实例失败: " + e.getMessage());
            return "redirect:/workflow/instances/create";
        }
    }    /**
     * 删除流程实例
     */
    @PostMapping("/{id}/delete")
    @PreAuthorize("hasRole('ADMIN')")
    public String deleteInstance(@PathVariable("id") Long instanceId, RedirectAttributes redirectAttributes) {
        logger.info("删除流程实例: {}", instanceId);

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            // 查找流程实例
            Optional<WorkflowInstance> instanceOpt = instanceService.findInstanceById(instanceId);
            if (!instanceOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程实例不存在");
                return "redirect:/workflow/instances";
            }
            // 执行删除操作
            boolean success = instanceService.deleteInstance(instanceId);

            if (success) {
                // 记录用户活动日志
                Optional<User> currentUser = userService.findUserByUsername(currentUsername);
                Long userId = currentUser.isPresent() ? currentUser.get().getUserId() : 0L; // 使用0作为默认ID，避免NULL值
                String ipAddress = getClientIpAddress();

                activityLogService.logDelete(
                        userId,
                        currentUsername,
                        "删除了流程实例: " + instanceId,
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());

                redirectAttributes.addFlashAttribute("message", "流程实例删除成功");
            } else {
                redirectAttributes.addFlashAttribute("error", "流程实例删除失败");
            }

            return "redirect:/workflow/instances";
        } catch (Exception e) {        logger.error("删除流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除流程实例时出错: " + e.getMessage());
            return "redirect:/workflow/instances";
        }
    }

    /**
     * 删除草稿状态的流程实例（发起人可以删除自己的草稿）
     */
    @PostMapping("/delete-draft")
    @PreAuthorize("isAuthenticated()")
    public String deleteDraftInstance(@RequestParam("instanceId") Long instanceId, RedirectAttributes redirectAttributes) {
        logger.info("删除草稿流程实例: {}", instanceId);

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            
            // 执行删除操作
            boolean success = instanceService.deleteDraftInstance(instanceId, currentUsername);

            if (success) {
                // 记录用户活动日志
                Optional<User> currentUser = userService.findUserByUsername(currentUsername);
                Long userId = currentUser.isPresent() ? currentUser.get().getUserId() : 0L;
                String ipAddress = getClientIpAddress();

                activityLogService.logDelete(
                        userId,
                        currentUsername,
                        "删除了草稿流程实例: " + instanceId,
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());

                redirectAttributes.addFlashAttribute("message", "草稿删除成功");
            } else {
                redirectAttributes.addFlashAttribute("error", "草稿删除失败");
            }

            return "redirect:/workflow/instances/my";
        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.warn("删除草稿流程实例时权限验证失败: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/workflow/instances/my";
        } catch (Exception e) {
            logger.error("删除草稿流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除草稿时出错: " + e.getMessage());
            return "redirect:/workflow/instances/my";
        }
    }/**
     * 下载工作流附件
     */
    @GetMapping("/download/attachment")
    public org.springframework.http.ResponseEntity<byte[]> downloadAttachment(
            @RequestParam("path") String attachmentPath) {

        logger.info("接收到工作流附件下载请求: path={}", attachmentPath);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));        // 权限检查：管理员可以直接下载
        if (!isAdmin) {
            // 非管理员需要检查是否是审批记录操作人
            boolean hasPermission = false;
            try {
                // 从附件路径查找对应的审批记录
                List<ApprovalRecord> records = recordService.findRecordsByAttachmentPath(attachmentPath);
                  if (!records.isEmpty()) {
                    // 获取该附件所属流程实例的所有审批记录操作人
                    Long instanceId = records.get(0).getInstance().getInstanceId();
                    List<ApprovalRecord> allInstanceRecords = recordService.findRecordsByInstanceId(instanceId);
                    Set<String> approvers = new HashSet<>();
                    
                    for (ApprovalRecord record : allInstanceRecords) {
                        if (record.getApprover() != null) {
                            approvers.add(record.getApprover());
                        }
                    }
                    
                    // 获取工作流实例的当前审批人并添加到审批人集合中
                    try {
                        Optional<WorkflowInstance> instanceOpt = instanceService.findInstanceById(instanceId);
                        if (instanceOpt.isPresent()) {
                            WorkflowInstance instance = instanceOpt.get();
                            if (instance.getCurrentApprover() != null && !instance.getCurrentApprover().trim().isEmpty()) {
                                approvers.add(instance.getCurrentApprover());
                                logger.info("已将工作流实例 {} 的当前审批人 {} 添加到审批人集合中", 
                                        instanceId, instance.getCurrentApprover());
                            }
                        }
                    } catch (Exception e) {
                        logger.error("获取工作流实例 {} 的当前审批人时出错: {}", instanceId, e.getMessage(), e);
                    }
                    
                    // 检查当前用户是否是审批记录操作人
                    hasPermission = approvers.contains(currentUsername);
                    
                    logger.info("附件路径: {}, 流程实例ID: {}, 审批记录操作人: {}, 当前用户: {}, 有权限: {}", 
                            attachmentPath, instanceId, approvers, currentUsername, hasPermission);
                } else {
                    logger.warn("未找到包含附件路径的审批记录: {}", attachmentPath);
                }
            } catch (Exception e) {
                logger.error("检查附件下载权限时出错: {}", e.getMessage(), e);
            }
            
            if (!hasPermission) {
                logger.warn("用户 {} 无权下载工作流附件: {}", currentUsername, attachmentPath);

                // 返回更优雅的提示信息而不是403状态码
                byte[] messageBytes = "您没有权限下载此文件，只有审批记录操作人才能下载附件。".getBytes();
                org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
                headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

                return org.springframework.http.ResponseEntity
                        .ok()
                        .headers(headers)
                        .contentLength(messageBytes.length)
                        .body(messageBytes);
            }
        }try {
            // 处理附件路径：如果路径以 "data/" 或 "data\" 开头，去掉这个前缀
            String processedPath = attachmentPath;
            if (attachmentPath.startsWith("data/") || attachmentPath.startsWith("data\\")) {
                processedPath = attachmentPath.substring(5);
                logger.info("处理附件路径: {} -> {}", attachmentPath, processedPath);
            }
            
            // 解析文件路径
            Path path = Paths.get(dataPath, processedPath);
            
            // 检查文件是否存在
            if (!Files.exists(path)) {
                logger.error("文件不存在: {}", path.toAbsolutePath());
                
                // 如果处理后的路径不存在，尝试使用原始路径
                Path originalPath = Paths.get(attachmentPath);
                if (Files.exists(originalPath)) {
                    logger.info("使用原始路径找到文件: {}", originalPath.toAbsolutePath());
                    path = originalPath;
                } else {
                    logger.error("原始路径也未找到文件: {}", originalPath.toAbsolutePath());
                    
                    // 返回友好的错误信息
                    String errorMessage = String.format("文件不存在或已被移动：%s", processedPath);
                    byte[] messageBytes = errorMessage.getBytes("UTF-8");
                    org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                    headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
                    headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

                    return org.springframework.http.ResponseEntity
                            .ok()
                            .headers(headers)
                            .contentLength(messageBytes.length)
                            .body(messageBytes);
                }
            }

            // 获取文件名
            String filename = path.getFileName().toString();

            byte[] data = Files.readAllBytes(path);
            logger.info("成功读取工作流附件文件: {}, 大小: {} bytes", path.toAbsolutePath(), data.length);

            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            // 处理文件名中可能包含的中文字符
            filename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename*=UTF-8''" + filename);

            // 记录下载活动日志
            try {
                // 获取当前用户ID
                Long userId = null;
                Optional<com.mylog.model.user.User> userOpt = userService.findUserByUsername(currentUsername);
                if (userOpt.isPresent()) {
                    userId = userOpt.get().getUserId();
                }

                // 构建下载描述信息
                String description = String.format("下载了工作流附件：%s", path.getFileName().toString());

                // 记录下载活动日志
                activityLogService.logDownload(
                        userId,
                        currentUsername,
                        description,
                        getClientIpAddress(),
                        "Workflow",
                        null,
                        getAccessType());

                logger.info("用户 {} 下载了工作流附件：{}", currentUsername, path.getFileName().toString());

            } catch (Exception e) {
                logger.error("记录下载活动日志时出错", e);
                // 不影响文件下载继续进行
            }

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(data.length)
                    .body(data);

        } catch (Exception e) {
            logger.error("下载工作流附件时出错: {}", e.getMessage(), e);
            
            // 返回错误信息
            String errorMessage = "下载文件时出错：" + e.getMessage();
            byte[] messageBytes = errorMessage.getBytes();
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
            headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(messageBytes.length)                    .body(messageBytes);
        }
    }
}
