<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('任务管理')}">
    <meta charset="UTF-8">
    <title>任务管理</title>
    <meta name="_csrf" th:content="${_csrf?.token}">
    <meta name="_csrf_header" th:content="${_csrf?.headerName}">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <script th:inline="javascript">
        // 将人员列表保存为全局变量
        const personnelList = /*[[${personnel}]]*/ [];
        console.log('人员列表:', personnelList); // 添加调试日志

        // 将视觉类型列表保存为全局变量
        const visionTypeList = /*[[${visionTypes}]]*/ [];
        console.log('视觉类型列表:', visionTypeList); // 添加调试日志
        console.log('视觉类型列表长度:', visionTypeList.length); // 检查列表长度
        console.log('视觉类型列表内容详细:', JSON.stringify(visionTypeList)); // 详细检查列表内容

        // 将任务类型列表保存为全局变量
        const taskTypeList = /*[[${taskTypes}]]*/ [];
        console.log('任务类型列表:', taskTypeList); // 添加调试日志

        function updateValueField(selectField) {
            console.log(`更新字段值: ${selectField.value}`);
            const valueContainer = selectField.closest('.search-condition').querySelector('.value-container');
            const selectedField = selectField.value;

            if (!selectedField) {
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }            // 检查是否已经存在特殊字段
            if (selectedField === 'createdDate' || selectedField === 'endTime' || selectedField === 'commentDays' || selectedField === 'taskStage' || selectedField === 'ratedDurationDays' || selectedField === 'remainingDurationDays') {
                // 检查是否已经存在相同类型的特殊字段
                const existingField = document.querySelector(`input[name="fieldNames"][value="${selectedField}"]`);
                if (existingField && existingField.closest('.search-condition') !== selectField.closest('.search-condition')) {
                    console.log(`已存在 ${selectedField} 字段，不能重复选择`);
                    alert(`已存在 ${selectedField} 字段，不能重复选择`);
                    selectField.value = '';
                    valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                    return;
                }
            }

            // 获取当前值（如果存在）
            const currentValue = valueContainer.querySelector('input, select')?.value || '';

            // 创建隐藏的字段名称输入
            let hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            hiddenField.value = selectedField;

            // 根据字段类型创建不同的输入控件
            let inputField;

            switch (selectedField) {
                case 'responsible':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    // 添加空选项
                    const emptyOption = document.createElement('option');
                    emptyOption.value = '';
                    emptyOption.textContent = '请选择负责人';
                    inputField.appendChild(emptyOption);

                    // 使用全局变量 personnelList
                    console.log('添加人员选项:', personnelList); // 添加调试日志
                    personnelList.forEach(person => {
                        const personOption = document.createElement('option');
                        personOption.value = person;
                        personOption.textContent = person;
                        inputField.appendChild(personOption);
                    });
                    inputField.value = currentValue;
                    break;
                case 'status':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    const statusOptions = [
                        {value: '未开始', text: '未开始'},
                        {value: '进行中', text: '进行中'},
                        {value: '已完成', text: '已完成'},
                        {value: '已暂停', text: '已暂停'}
                    ];

                    // 添加选项
                    statusOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = currentValue;
                    break;
                case 'risk':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    const riskOptions = [
                        {value: '正常', text: '正常'},
                        {value: '中', text: '中'},
                        {value: '高', text: '高'}
                    ];

                    // 添加选项
                    riskOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = currentValue;
                    break;
                case 'type':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    // 添加空选项
                    const emptyTypeOption = document.createElement('option');
                    emptyTypeOption.value = '';
                    emptyTypeOption.textContent = '请选择任务类型';
                    inputField.appendChild(emptyTypeOption);

                    // 使用全局变量 taskTypeList
                    if (taskTypeList && taskTypeList.length > 0) {
                        taskTypeList.forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            typeOption.selected = (type === currentValue);
                            inputField.appendChild(typeOption);
                        });
                    } else {
                        console.warn('任务类型列表为空，使用默认值');
                        const defaultTypes = ['订单', '难点', '专项', '培训', '分管'];
                        defaultTypes.forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            typeOption.selected = (type === currentValue);
                            inputField.appendChild(typeOption);
                        });
                    }
                    break;
                case 'visionType':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    // 添加空选项
                    const emptyVisionOption = document.createElement('option');
                    emptyVisionOption.value = '';
                    emptyVisionOption.textContent = '请选择视觉类型';
                    inputField.appendChild(emptyVisionOption);

                    console.log('更新视觉类型字段，视觉类型列表:', visionTypeList);
                    console.log('当前值:', currentValue);

                    // 使用全局变量 visionTypeList
                    if (visionTypeList && visionTypeList.length > 0) {
                        visionTypeList.forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            typeOption.selected = (type === currentValue);
                            inputField.appendChild(typeOption);
                        });
                    } else {
                        console.warn('视觉类型列表为空或不存在');
                        // 使用备选值
                        ['2D', '3D', 'OCR', '其他'].forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            typeOption.selected = (type === currentValue);
                            inputField.appendChild(typeOption);
                        });
                    }
                    break;
                case 'taskStage':
                    // 创建任务阶段范围输入
                    const rangeContainer = document.createElement('div');
                    rangeContainer.className = 'row';

                    // 最小值输入框
                    const minContainer = document.createElement('div');
                    minContainer.className = 'col-6';
                    const minGroup = document.createElement('div');
                    minGroup.className = 'input-group';

                    const minLabel = document.createElement('span');
                    minLabel.className = 'input-group-text';
                    minLabel.textContent = '最小值';

                    const minInput = document.createElement('input');
                    minInput.type = 'number';
                    minInput.className = 'form-control';
                    minInput.name = 'field_taskStage_min';
                    minInput.min = '0';
                    minInput.placeholder = '0';

                    minGroup.appendChild(minLabel);
                    minGroup.appendChild(minInput);
                    minContainer.appendChild(minGroup);

                    // 最大值输入框
                    const maxContainer = document.createElement('div');
                    maxContainer.className = 'col-6';
                    const maxGroup = document.createElement('div');
                    maxGroup.className = 'input-group';

                    const maxLabel = document.createElement('span');
                    maxLabel.className = 'input-group-text';
                    maxLabel.textContent = '最大值';

                    const maxInput = document.createElement('input');
                    maxInput.type = 'number';
                    maxInput.className = 'form-control';
                    maxInput.name = 'field_taskStage_max';
                    maxInput.min = '0';
                    maxInput.placeholder = '不限';

                    maxGroup.appendChild(maxLabel);
                    maxGroup.appendChild(maxInput);
                    maxContainer.appendChild(maxGroup);

                    // 组装范围容器
                    rangeContainer.appendChild(minContainer);
                    rangeContainer.appendChild(maxContainer);

                    // 创建隐藏的字段名称输入
                    const hiddenTaskStageField = document.createElement('input');
                    hiddenTaskStageField.type = 'hidden';
                    hiddenTaskStageField.name = 'fieldNames';
                    hiddenTaskStageField.value = 'taskStage';

                    // 清空并添加新的输入字段
                    valueContainer.innerHTML = '';
                    valueContainer.appendChild(hiddenTaskStageField);
                    valueContainer.appendChild(rangeContainer);

                    return; // 直接返回，不执行后面的代码
                case 'ratedDurationDays':
                    // 创建额定工期范围输入
                    const ratedDurationRangeContainer = document.createElement('div');
                    ratedDurationRangeContainer.className = 'row';

                    // 最小值输入框
                    const ratedDurationMinContainer = document.createElement('div');
                    ratedDurationMinContainer.className = 'col-6';
                    const ratedDurationMinGroup = document.createElement('div');
                    ratedDurationMinGroup.className = 'input-group';

                    const ratedDurationMinLabel = document.createElement('span');
                    ratedDurationMinLabel.className = 'input-group-text';
                    ratedDurationMinLabel.textContent = '最小值';

                    const ratedDurationMinInput = document.createElement('input');
                    ratedDurationMinInput.type = 'number';
                    ratedDurationMinInput.className = 'form-control';
                    ratedDurationMinInput.name = 'field_ratedDurationDays_min';
                    ratedDurationMinInput.min = '0';
                    ratedDurationMinInput.step = '0.1';
                    ratedDurationMinInput.placeholder = '0';

                    ratedDurationMinGroup.appendChild(ratedDurationMinLabel);
                    ratedDurationMinGroup.appendChild(ratedDurationMinInput);
                    ratedDurationMinContainer.appendChild(ratedDurationMinGroup);

                    // 最大值输入框
                    const ratedDurationMaxContainer = document.createElement('div');
                    ratedDurationMaxContainer.className = 'col-6';
                    const ratedDurationMaxGroup = document.createElement('div');
                    ratedDurationMaxGroup.className = 'input-group';

                    const ratedDurationMaxLabel = document.createElement('span');
                    ratedDurationMaxLabel.className = 'input-group-text';
                    ratedDurationMaxLabel.textContent = '最大值';

                    const ratedDurationMaxInput = document.createElement('input');
                    ratedDurationMaxInput.type = 'number';
                    ratedDurationMaxInput.className = 'form-control';
                    ratedDurationMaxInput.name = 'field_ratedDurationDays_max';
                    ratedDurationMaxInput.min = '0';
                    ratedDurationMaxInput.step = '0.1';
                    ratedDurationMaxInput.placeholder = '不限';

                    ratedDurationMaxGroup.appendChild(ratedDurationMaxLabel);
                    ratedDurationMaxGroup.appendChild(ratedDurationMaxInput);
                    ratedDurationMaxContainer.appendChild(ratedDurationMaxGroup);

                    // 组装范围容器
                    ratedDurationRangeContainer.appendChild(ratedDurationMinContainer);
                    ratedDurationRangeContainer.appendChild(ratedDurationMaxContainer);

                    // 创建隐藏的字段名称输入
                    const hiddenRatedDurationField = document.createElement('input');
                    hiddenRatedDurationField.type = 'hidden';
                    hiddenRatedDurationField.name = 'fieldNames';
                    hiddenRatedDurationField.value = 'ratedDurationDays';

                    // 清空并添加新的输入字段
                    valueContainer.innerHTML = '';
                    valueContainer.appendChild(hiddenRatedDurationField);
                    valueContainer.appendChild(ratedDurationRangeContainer);                    return; // 直接返回，不执行后面的代码
                case 'remainingDurationDays':
                    // 创建剩余工期范围输入
                    const remainingDurationRangeContainer = document.createElement('div');
                    remainingDurationRangeContainer.className = 'row';

                    // 最小值输入框
                    const remainingDurationMinContainer = document.createElement('div');
                    remainingDurationMinContainer.className = 'col-6';
                    const remainingDurationMinGroup = document.createElement('div');
                    remainingDurationMinGroup.className = 'input-group';

                    const remainingDurationMinLabel = document.createElement('span');
                    remainingDurationMinLabel.className = 'input-group-text';
                    remainingDurationMinLabel.textContent = '最小值';

                    const remainingDurationMinInput = document.createElement('input');
                    remainingDurationMinInput.type = 'number';
                    remainingDurationMinInput.className = 'form-control';
                    remainingDurationMinInput.name = 'field_remainingDurationDays_min';
                    remainingDurationMinInput.step = '0.1';
                    remainingDurationMinInput.placeholder = '不限';

                    remainingDurationMinGroup.appendChild(remainingDurationMinLabel);
                    remainingDurationMinGroup.appendChild(remainingDurationMinInput);
                    remainingDurationMinContainer.appendChild(remainingDurationMinGroup);

                    // 最大值输入框
                    const remainingDurationMaxContainer = document.createElement('div');
                    remainingDurationMaxContainer.className = 'col-6';
                    const remainingDurationMaxGroup = document.createElement('div');
                    remainingDurationMaxGroup.className = 'input-group';

                    const remainingDurationMaxLabel = document.createElement('span');
                    remainingDurationMaxLabel.className = 'input-group-text';
                    remainingDurationMaxLabel.textContent = '最大值';

                    const remainingDurationMaxInput = document.createElement('input');
                    remainingDurationMaxInput.type = 'number';
                    remainingDurationMaxInput.className = 'form-control';
                    remainingDurationMaxInput.name = 'field_remainingDurationDays_max';
                    remainingDurationMaxInput.step = '0.1';
                    remainingDurationMaxInput.placeholder = '不限';

                    remainingDurationMaxGroup.appendChild(remainingDurationMaxLabel);
                    remainingDurationMaxGroup.appendChild(remainingDurationMaxInput);
                    remainingDurationMaxContainer.appendChild(remainingDurationMaxGroup);

                    // 组装范围容器
                    remainingDurationRangeContainer.appendChild(remainingDurationMinContainer);
                    remainingDurationRangeContainer.appendChild(remainingDurationMaxContainer);

                    // 创建隐藏的字段名称输入
                    const hiddenRemainingDurationField = document.createElement('input');
                    hiddenRemainingDurationField.type = 'hidden';
                    hiddenRemainingDurationField.name = 'fieldNames';
                    hiddenRemainingDurationField.value = 'remainingDurationDays';

                    // 清空并添加新的输入字段
                    valueContainer.innerHTML = '';
                    valueContainer.appendChild(hiddenRemainingDurationField);
                    valueContainer.appendChild(remainingDurationRangeContainer);

                    return; // 直接返回，不执行后面的代码
                default:
                    inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'form-control';
                    inputField.name = 'field_' + selectedField;
                    inputField.placeholder = '请输入' + selectField.options[selectField.selectedIndex].text;
                    inputField.value = '';
            }

            // 清空并添加新的输入字段
            valueContainer.innerHTML = '';
            valueContainer.appendChild(hiddenField);
            valueContainer.appendChild(inputField);
        }
    </script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}" th:with="activeMenu='taskmanagement'">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="d-flex align-items-center">
                        <h1 class="h2 mb-0">任务管理 <small class="fs-6">（<span class="badge bg-primary rounded-pill" th:text="'进行中 ' + ${allInProgressTaskCount}">进行中 23</span>/<span th:text="${taskPage.totalElements}">52</span>）</small></h1>
                    </div>                    <div th:if="${#authorization.expression('hasRole(''ADMIN'')') or #authentication.name == '邓利鹏'}">
                        <button type="button"
                                class="btn btn-success me-2"
                                onclick="updateAllInProgressTasksDuration()"
                                id="updateAllInProgressTasksDurationBtn"
                                th:if="${#authorization.expression('hasRole(''ADMIN'')') or #authentication.name == '邓利鹏'}">
                            更新所有进行中任务的实际工期
                        </button>
                        <button type="button"
                                class="btn btn-warning"
                                onclick="updateAllTasksDurationFields()"
                                id="updateAllTasksDurationFieldsBtn"
                                th:if="${#authorization.expression('hasRole(''ADMIN'')')}">
                            <i class="bi bi-arrow-clockwise"></i> 修复所有任务的累计工期和剩余工期
                        </button>
                    </div>
                </div>


                <!-- 搜索栏 -->
                <div class="row mb-3">
                    <div class="col-md-12">

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">任务搜索</h5>
                                <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="searchCollapse">
                                <div class="card-body">
                                    <!-- 高级搜索 -->                                    <form th:action="@{/tasks/advanced-search}" method="get" class="row g-3" id="advancedSearchForm">
                                        <!-- 返回位置参数 -->
                                        <input type="hidden" name="returnTo" value="management">
                                        <!-- 原始来源参数 -->
                                        <input type="hidden" name="originalSource" th:value="${param.originalSource?.get(0)}">
                                        <!-- 动态搜索条件 -->
                                        <div id="searchConditions">
                                            <div class="search-condition row mb-3">
                                                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">                                                    <select class="form-select search-field" onchange="updateValueField(this)">
                                                        <option value="">选择字段</option>
                                                        <option value="taskName">任务名称</option>
                                                        <option value="responsible">负责人</option>
                                                        <option value="status">状态</option>
                                                        <option value="risk">风险等级</option>
                                                        <option value="type">任务类型</option>
                                                        <option value="projectName">所属项目</option>
                                                        <option value="customerName">客户名称</option>
                                                        <option value="visionType">视觉类型</option>
                                                        <option value="taskStage">任务阶段</option>
                                                        <option value="ratedDurationDays">额定工期</option>
                                                        <option value="remainingDurationDays">剩余工期</option>
                                                    </select>
                                                </div>
                                                <div class="col-10 col-sm-5 col-md-7 value-container">
                                                    <!-- 值输入框将根据选择的字段动态生成 -->
                                                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                                                </div>
                                                <div class="col-2 col-sm-1 col-md-2">
                                                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 按钮组 - 更新为完全响应式布局 -->
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <div class="d-flex flex-wrap gap-2">
                                                    <button type="button" class="btn btn-outline-primary" onclick="addSearchCondition()">
                                                        <i class="bi bi-plus"></i> 添加条件
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" onclick="addTimeCondition()">
                                                        <i class="bi bi-calendar"></i> 添加创建时间条件
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" onclick="addEndTimeCondition()">
                                                        <i class="bi bi-calendar-check"></i> 添加结束时间条件
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" onclick="addCommentDaysCondition()">
                                                        <i class="bi bi-chat-dots"></i> 添加评论条件
                                                    </button>
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="bi bi-search"></i> 应用筛选
                                                    </button>
                                                    <button type="button" class="btn btn-info" onclick="showSaveSearchModal()">
                                                        <i class="bi bi-save"></i> 保存方案
                                                    </button>
                                                    <button type="button" class="btn btn-secondary" onclick="showLoadSearchModal()">
                                                        <i class="bi bi-folder-symlink"></i> 加载方案
                                                    </button>
                                                    <a th:href="@{/tasks/management}" class="btn btn-outline-secondary">
                                                        <i class="bi bi-arrow-counterclockwise"></i> 重置
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            任务列表
                            <span th:if="${taskPage.content != null && !taskPage.content.empty}" class="ms-3 text-info" style="font-size: 1rem; font-weight: normal;">
                                绩效分总和：
                                <span th:text="${#numbers.formatDecimal(bonusTotal, 1, 2)}">0.00</span>
                            </span>
                        </h5>
                        <div class="d-flex align-items-center">
                            <div class="btn-group">
                                <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#taskListCollapse" aria-expanded="true" aria-controls="taskListCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="collapse show" id="taskListCollapse">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 15%; white-space: nowrap;">任务名称</th>
                                            <th style="width: 15%; white-space: nowrap;">所属项目</th>
                                            <th style="width: 6%; white-space: nowrap;">负责人</th>
                                            <th style="width: 6%; white-space: nowrap;">绩效分</th>                                            <th style="width: 8%; white-space: nowrap;">额定工期</th>
                                            <th style="width: 8%; white-space: nowrap;">累计工期</th>
                                            <th style="width: 8%; white-space: nowrap;">剩余工期</th>
                                            <th style="width: 6%; white-space: nowrap;">状态</th>
                                            <th style="width: 7%; white-space: nowrap;">视觉类型</th>
                                            <th style="width: 6%; white-space: nowrap;">评论天数</th>
                                            <th style="width: 9%; white-space: nowrap;">结束时间</th>
                                            <th style="width: 9%; white-space: nowrap;">创建时间</th>
                                        </tr>
                                    </thead>                                    <tbody>                                        <tr th:if="${taskPage.content.empty}">
                                            <td colspan="12" class="text-center">暂无任务数据</td>
                                        </tr>

                                        <!-- 数据行 -->
                                        <tr th:each="task : ${taskPage.content}">
                                            <td>
                                                <a th:href="@{/tasks/{id}(id=${task.taskId})}" th:text="${task.taskName}">任务名称</a>
                                            </td>
                                            <td>
                                                <a th:if="${task.project != null}"
                                                   th:href="@{/projects/{id}(id=${task.projectId})}"
                                                   th:text="${task.project.projectName}"
                                                   th:title="${task.project.projectName}"
                                                   style="display: inline-block; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                    项目名称
                                                </a>
                                                <span th:if="${task.project == null && task.projectId != null}"
                                                      class="text-danger">项目不存在</span>
                                                <span th:if="${task.projectId == null}"
                                                      class="text-muted">-</span>
                                            </td>
                                            <td th:text="${task.responsible}">负责人</td>
                                            <td th:text="${task.bonus != null ? #numbers.formatDecimal(task.bonus, 1, 2) : '-'}">-</td>
                                            <td th:text="${task.ratedDurationDays != null ? task.ratedDurationDays + ' 天' : '-'}">-</td>                                            <td th:text="${task.cumulativeDurationDays != null ? #numbers.formatDecimal(task.cumulativeDurationDays, 1, 2) + ' 天' : '-'}"
                                                style="white-space: nowrap;">-</td>
                                            <td th:text="${task.remainingDurationDays != null ? #numbers.formatDecimal(task.remainingDurationDays, 1, 2) + ' 天' : '-'}"
                                                th:style="${task.remainingDurationDays != null && task.remainingDurationDays < 0 ? 'white-space: nowrap; color: red; font-weight: bold;' : 'white-space: nowrap;'}"
                                                style="white-space: nowrap;">-</td>
                                            <td><span th:class="${'badge ' +
                                                    (task.status == '进行中' ? 'bg-primary' :
                                                    (task.status == '已完成' ? 'bg-success' :
                                                    (task.status == '未开始' ? 'bg-secondary' :
                                                    (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                                    th:text="${task.status}">状态</span>
                                            </td>
                                            <td th:text="${task.project != null && task.project.visionType != null ? task.project.visionType.replace(',', '、') : '-'}" style="white-space: nowrap;">-</td>                                            <td>
                                                <span th:if="${task.commentDays != null}"
                                                      th:class="${'badge ' +
                                                        (task.commentDays >= 7 ? 'bg-danger' :
                                                        (task.commentDays >= 4 ? 'bg-warning' :
                                                        (task.commentDays >= 0 ? 'bg-success' : 'bg-dark')))}"
                                                      th:text="${task.commentDays}">0.0</span>
                                                <span th:unless="${task.commentDays != null}">-</span>
                                            </td>
                                            <td th:text="${task.actualEndDateTime != null ? #temporals.format(task.actualEndDateTime, 'yyyy-MM-dd') : '-'}" style="white-space: nowrap;">-</td>
                                            <td th:text="${task.createdDateTime != null ? #temporals.format(task.createdDateTime, 'yyyy-MM-dd') : '-'}" style="white-space: nowrap;">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- 分页控件 -->
                        <div class="card-footer" th:if="${taskPage.totalPages > 0}">
                            <div th:replace="~{fragments/pagination :: pagination(${taskPage}, @{/tasks/management-tasks})}"></div>
                        </div>
                    </div>
                </div>
    </div>

    <script th:src="@{/js/history-tracker.js}"></script>
    <script>
        // 修复所有任务累计工期和剩余工期 按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            var btn = document.getElementById('updateAllTasksDurationFieldsBtn');
            if (btn) {
                btn.addEventListener('click', async function() {
                    // 防止重复点击
                    if (btn.disabled) {
                        return;
                    }
                    
                    if (!confirm('确定要修复所有任务的累计工期和剩余工期吗？该操作可能耗时较长。')) {
                        return;
                    }

                    // 立即禁用按钮防止重复点击
                    btn.disabled = true;
                    let dots = '';
                    const loadingInterval = setInterval(() => {
                        dots = dots.length >= 3 ? '' : dots + '.';
                        btn.innerText = `修复中${dots}`;
                    }, 500);

                    // 添加超时处理
                    const timeout = setTimeout(() => {
                        clearInterval(loadingInterval);
                        btn.disabled = false;
                        btn.innerText = '修复所有任务的累计工期和剩余工期';
                        alert('操作超时，请稍后重试。建议分批次处理数据。');
                    }, 300000); // 5分钟超时

                    // 发送请求并显示进度
                    fetch('/tasks/update-all-tasks-duration-fields', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(res => {
                        if (!res.ok) {
                            throw new Error(res.statusText);
                        }
                        return res.json();
                    })
                    .then(data => {
                        clearInterval(loadingInterval);
                        clearTimeout(timeout);
                        btn.disabled = false;
                        btn.innerText = '修复所有任务的累计工期和剩余工期';
                        
                        if (data && data.showAlert) {
                            const successMessage = data.message || '操作完成';
                            // 使用更友好的提示方式
                            const messageDiv = document.createElement('div');
                            messageDiv.className = 'alert alert-success alert-dismissible fade show fixed-top mx-auto mt-3';
                            messageDiv.style.maxWidth = '500px';
                            messageDiv.innerHTML = `
                                <strong>${successMessage}</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            `;
                            document.body.appendChild(messageDiv);
                            
                            // 3秒后自动隐藏
                            setTimeout(() => {
                                messageDiv.remove();
                                // 刷新页面以显示更新后的数据
                                window.location.reload();
                            }, 3000);
                        }
                    })
                    .catch(err => {
                        clearInterval(loadingInterval);
                        clearTimeout(timeout);
                        btn.disabled = false;
                        btn.innerText = '修复所有任务的累计工期和剩余工期';
                        
                        // 显示错误信息
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'alert alert-danger alert-dismissible fade show fixed-top mx-auto mt-3';
                        errorDiv.style.maxWidth = '500px';
                        errorDiv.innerHTML = `
                            <strong>操作失败：</strong> ${err.message || '请稍后重试'}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.body.appendChild(errorDiv);
                        
                        // 5秒后自动隐藏错误信息
                        setTimeout(() => errorDiv.remove(), 5000);
                    });
                });
            }
        });
        // 页面加载完成后检查模态框是否存在
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，检查模态框元素');
            const saveModal = document.getElementById('saveSearchModal');
            const loadModal = document.getElementById('loadSearchModal');
            console.log('保存方案模态框元素:', saveModal ? '存在' : '不存在');
            console.log('加载方案模态框元素:', loadModal ? '存在' : '不存在');

            // 如果模态框不存在，创建模态框
            if (!loadModal) {
                console.log('加载方案模态框不存在，创建模态框');
                createLoadSearchModal();
            }

            if (!saveModal) {
                console.log('保存方案模态框不存在，创建模态框');
                createSaveSearchModal();
            }
        });

        // 创建加载方案模态框
        function createLoadSearchModal() {
            const modalHtml = `
                <div class="modal fade" id="loadSearchModal" tabindex="-1" aria-labelledby="loadSearchModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="loadSearchModalLabel">加载搜索方案</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body">
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped" id="searchPlanTable">
                                        <thead>
                                            <tr>
                                                <th>方案名称</th>
                                                <th>描述</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 这里将通过JavaScript动态填充 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div id="noSearchPlans" class="text-center p-3 d-none">
                                    <p class="mb-0">暂无保存的搜索方案</p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 将模态框添加到页面中
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            console.log('已创建加载方案模态框');
        }

        // 创建保存方案模态框
        function createSaveSearchModal() {
            const modalHtml = `
                <div class="modal fade" id="saveSearchModal" tabindex="-1" aria-labelledby="saveSearchModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="saveSearchModalLabel">保存搜索方案</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="searchPlanName" class="form-label">方案名称</label>
                                    <input type="text" class="form-control" id="searchPlanName" placeholder="输入方案名称">
                                </div>
                                <div class="mb-3">
                                    <label for="searchPlanDesc" class="form-label">方案描述（可选）</label>
                                    <textarea class="form-control" id="searchPlanDesc" rows="3" placeholder="输入方案描述"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="saveSearchPlan()">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 将模态框添加到页面中
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            console.log('已创建保存方案模态框');
        }

        // 添加搜索条件
        function addSearchCondition() {
            console.log('添加新的搜索条件');

            // 创建新的搜索条件元素，而不是克隆现有元素
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';

            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';            // 创建字段选择下拉框
            const select = document.createElement('select');
            select.className = 'form-select search-field';
            select.setAttribute('onchange', 'updateValueField(this)');            select.innerHTML = `
                <option value="">选择字段</option>
                <option value="taskName">任务名称</option>
                <option value="responsible">负责人</option>
                <option value="status">状态</option>
                <option value="risk">风险等级</option>
                <option value="type">任务类型</option>
                <option value="projectName">所属项目</option>
                <option value="customerName">客户名称</option>
                <option value="visionType">视觉类型</option>
                <option value="taskStage">任务阶段</option>
                <option value="ratedDurationDays">额定工期</option>
                <option value="remainingDurationDays">剩余工期</option>
            `;
            fieldCol.appendChild(select);

            // 创建值容器
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7 value-container';
            valueCol.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';

            // 创建删除按钮
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';

            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-outline-danger w-100';
            deleteBtn.setAttribute('onclick', 'removeCondition(this)');
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            btnCol.appendChild(deleteBtn);

            // 组装条件元素
            newCondition.appendChild(fieldCol);
            newCondition.appendChild(valueCol);
            newCondition.appendChild(btnCol);

            // 添加到搜索条件容器
            document.getElementById('searchConditions').appendChild(newCondition);
            console.log('已添加新的搜索条件');
        }

        // 添加创建时间条件
        function addTimeCondition() {
            console.log('添加创建时间条件');

            // 检查是否已经存在时间条件
            const existingTimeCondition = document.querySelector('input[name="fieldNames"][value="createdDate"]');
            if (existingTimeCondition) {
                console.log('已存在创建时间条件，不重复添加');
                alert('已存在创建时间条件，不能重复添加');
                return;
            }

            const conditions = document.getElementById('searchConditions');
            const timeCondition = document.createElement('div');
            timeCondition.className = 'search-condition row mb-3';
            timeCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" name="fieldNames" disabled>
                        <option value="createdDate" selected>创建日期</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="createdDate">
                </div>
                <div class="col-10 col-sm-5 col-md-7">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">从</span>
                                <input type="date" class="form-control" name="field_createdDate_start">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" name="field_createdDate_end">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(timeCondition);
            console.log('已添加时间条件');
        }

        // 添加结束时间条件
        function addEndTimeCondition() {
            console.log('添加结束时间条件');

            // 检查是否已经存在结束时间条件
            const existingEndTimeCondition = document.querySelector('input[name="fieldNames"][value="actualEndDate"]');
            if (existingEndTimeCondition) {
                console.log('已存在结束时间条件，不重复添加');
                alert('已存在结束时间条件，不能重复添加');
                return;
            }

            const conditions = document.getElementById('searchConditions');
            const endTimeCondition = document.createElement('div');
            endTimeCondition.className = 'search-condition row mb-3';
            endTimeCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" name="fieldNames" disabled>
                        <option value="actualEndDate" selected>结束时间</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="actualEndDate">
                </div>
                <div class="col-10 col-sm-5 col-md-7">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">从</span>
                                <input type="date" class="form-control" name="field_actualEndDate_start">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" name="field_actualEndDate_end">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(endTimeCondition);        console.log('已添加结束时间条件');
        }

        // 添加评论天数条件
        function addCommentDaysCondition() {
            console.log('添加评论天数条件');

            // 检查是否已经存在评论天数条件
            const existingCommentDaysCondition = document.querySelector('input[name="fieldNames"][value="commentDays"]');
            if (existingCommentDaysCondition) {
                console.log('已存在评论天数条件，不重复添加');
                alert('已存在评论天数条件，不能重复添加');
                return;
            }

            const conditions = document.getElementById('searchConditions');
            const commentCondition = document.createElement('div');
            commentCondition.className = 'search-condition row mb-3';
            commentCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" disabled>
                        <option value="commentDays" selected>评论天数</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="commentDays">
                </div>
                <div class="col-10 col-sm-5 col-md-7">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">最小</span>
                                <input type="number" class="form-control" name="field_commentDays_min" min="-1000" placeholder="0">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">最大</span>
                                <input type="number" class="form-control" name="field_commentDays_max" min="0" placeholder="不限">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(commentCondition);
            console.log('已添加评论天数条件');
        }

        // 分页处理
        document.addEventListener('DOMContentLoaded', function() {
            // 处理分页点击
            document.querySelectorAll('.pagination-link').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 获取目标页码
                    const page = this.getAttribute('data-page');
                    if (!page) return;

                    // 获取当前URL和参数
                    let url = new URL(window.location.href);
                    let params = url.searchParams;

                    // 更新页码参数
                    params.set('page', page);

                    // 确保当前在高级搜索状态下保持搜索URL
                    const isSearchMode = Array.from(params.keys()).some(key => key.startsWith('field_') || key === 'fieldNames');                    if (isSearchMode) {
                        // 如果在高级搜索模式下，确保URL路径正确
                        if (url.pathname !== '/tasks/advanced-search') {
                            url.pathname = '/tasks/advanced-search';

                            // 添加返回位置参数（如果不存在）
                            if (!params.has('returnTo')) {
                                params.set('returnTo', 'management');
                            }
                            
                            // 保持originalSource参数（如果存在）
                            const originalSource = new URLSearchParams(window.location.search).get('originalSource');
                            if (originalSource && !params.has('originalSource')) {
                                params.set('originalSource', originalSource);
                            }
                        }
                    }

                    // 构建新的URL
                    url.search = params.toString();

                    // 跳转到新URL
                    window.location.href = url.toString();
                });
            });

            // 为表单添加提交验证
            document.getElementById('advancedSearchForm').addEventListener('submit', function(e) {
                console.log('表单提交事件触发');
                // 检查是否有有效的搜索条件
                const hasValidCondition = Array.from(document.querySelectorAll('.search-field'))
                    .some(select => select.value !== '');

                // 检查是否有特殊条件（日期条件、评论条件或任务阶段条件）                const hasSpecialCondition =
                    document.querySelectorAll('input[name="fieldNames"][value="createdDate"]').length > 0 ||
                    document.querySelectorAll('input[name="fieldNames"][value="endTime"]').length > 0 ||
                    document.querySelectorAll('input[name="fieldNames"][value="commentDays"]').length > 0 ||
                    document.querySelectorAll('input[name="fieldNames"][value="taskStage"]').length > 0;

                if (!hasValidCondition && !hasSpecialCondition) {
                    e.preventDefault();
                    alert('请至少选择一个搜索字段');
                    return false;
                }

                // 检查并处理重复的字段名
                const fieldNameInputs = document.querySelectorAll('input[name="fieldNames"]');
                const fieldNameValues = {};
                const processedFields = new Set(); // 记录已处理的字段

                console.log(`开始处理字段名，共找到 ${fieldNameInputs.length} 个字段名输入框`);

                // 统计各个字段名的出现次数
                fieldNameInputs.forEach(input => {
                    const value = input.value;
                    if (value) {
                        fieldNameValues[value] = (fieldNameValues[value] || 0) + 1;
                        console.log(`字段名: ${value}, 当前计数: ${fieldNameValues[value]}`);
                    }
                });

                console.log('字段名统计:', fieldNameValues);

                // 处理重复的字段名
                for (const [fieldName, count] of Object.entries(fieldNameValues)) {
                    if (count > 1) {
                        console.log(`发现重复字段名: ${fieldName}, 次数: ${count}`);

                        // 保留第一个字段名，删除其余的
                        let found = false;
                        fieldNameInputs.forEach(input => {
                            if (input.value === fieldName) {
                                if (found) {
                                    // 如果已经找到了一个，则禁用当前这个
                                    input.name = 'fieldNames_disabled';
                                    console.log(`禁用重复的字段名输入框: ${fieldName}`);
                                } else {
                                    found = true;
                                    console.log(`保留字段名: ${fieldName}`);
                                }
                            }
                        });
                    }
                }

                // 检查特殊字段的重复情况
                const specialFields = ['createdDate', 'endTime', 'commentDays', 'taskStage'];
                specialFields.forEach(specialField => {
                    // 检查是否有多个相同类型的特殊字段
                    const specialInputs = Array.from(fieldNameInputs).filter(input => input.value === specialField && input.name === 'fieldNames');
                    console.log(`特殊字段 ${specialField} 找到 ${specialInputs.length} 个`);
                    if (specialInputs.length > 1) {
                        console.log(`发现多个 ${specialField} 字段，保留第一个，禁用其余的`);
                        // 保留第一个，禁用其余的
                        for (let i = 1; i < specialInputs.length; i++) {
                            specialInputs[i].name = 'fieldNames_disabled';
                            console.log(`已禁用第 ${i+1} 个 ${specialField} 字段`);
                        }
                    }
                });

                // 确保表单提交到正确的URL
                this.action = window.location.origin + '/tasks/advanced-search';                // 添加返回位置参数
                const returnToField = document.querySelector('input[name="returnTo"]');
                if (!returnToField) {
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'returnTo';
                    hiddenField.value = 'management';
                    this.appendChild(hiddenField);
                }
                
                // 保持originalSource参数（如果存在）
                const existingOriginalSource = document.querySelector('input[name="originalSource"]');
                if (!existingOriginalSource) {
                    const originalSource = new URLSearchParams(window.location.search).get('originalSource');
                    if (originalSource) {
                        const originalSourceField = document.createElement('input');
                        originalSourceField.type = 'hidden';
                        originalSourceField.name = 'originalSource';
                        originalSourceField.value = originalSource;
                        this.appendChild(originalSourceField);
                    }
                }

                // 检查表单中的字段名，确保评论天数条件被正确提交
                const formData = new FormData(this);
                console.log('表单提交前的字段:');
                for (const [key, value] of formData.entries()) {
                    console.log(`${key} = ${value}`);
                }                // 检查是否有评论天数相关的输入字段但没有对应的fieldNames
                const hasCommentMinField = formData.has('field_commentDays_min');
                const hasCommentMaxField = formData.has('field_commentDays_max');
                const hasCommentDaysFieldName = Array.from(formData.getAll('fieldNames')).includes('commentDays');

                if ((hasCommentMinField || hasCommentMaxField) && !hasCommentDaysFieldName) {
                    console.log('发现评论天数输入字段但没有对应的fieldNames，添加fieldNames=commentDays');
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'fieldNames';
                    hiddenField.value = 'commentDays';
                    hiddenField.className = 'comment-days-field-added';
                    this.appendChild(hiddenField);
                    console.log('已添加评论天数字段名，确保条件正确传递');
                }

                console.log('表单提交验证通过，准备提交');
                return true;
            });

            // 恢复搜索条件（如果存在）
            restoreSearchConditions();

            // 确保至少有一个搜索条件
            if (document.querySelectorAll('.search-condition').length === 0) {
                console.log('没有搜索条件，添加一个空的搜索条件');
                addSearchCondition();
            }
        });

        // 恢复搜索条件
        function restoreSearchConditions() {
            console.log('开始恢复搜索条件');
            // 检查URL是否包含搜索参数
            const url = new URL(window.location.href);
            const params = url.searchParams;
            const searchParams = {};

            // 收集所有搜索参数
            for (const [key, value] of params.entries()) {
                if (key.startsWith('field_') || key === 'fieldNames') {
                    searchParams[key] = value;
                }
            }

            // 如果没有搜索参数则不处理
            if (Object.keys(searchParams).length === 0) {
                console.log('没有搜索参数，不需要恢复');
                return;
            }

            console.log('恢复搜索条件:', searchParams);

            // 获取所有字段名
            const fieldNames = params.getAll('fieldNames');
            if (!fieldNames || fieldNames.length === 0) {
                console.log('没有字段名参数，不需要恢复');
                return;
            }

            // 处理重复的字段名
            const uniqueFieldNames = [];
            fieldNames.forEach(fieldName => {
                if (!uniqueFieldNames.includes(fieldName)) {
                    uniqueFieldNames.push(fieldName);
                }
            });
            console.log('去重后的字段名:', uniqueFieldNames);

            // 移除默认的空搜索条件
            const searchConditions = document.getElementById('searchConditions');
            while (searchConditions.firstChild) {
                searchConditions.removeChild(searchConditions.firstChild);
            }
            console.log('已清空现有搜索条件');

            // 重建搜索条件
            uniqueFieldNames.forEach(fieldName => {
                console.log(`处理字段: ${fieldName}`);
                // 处理时间范围条件
                if (fieldName === 'createdDate') {
                    const startDate = params.get('field_createdDate_start');
                    const endDate = params.get('field_createdDate_end');
                    console.log(`恢复时间条件: 开始=${startDate}, 结束=${endDate}`);
                    addTimeConditionWithValues(startDate, endDate);
                    return;
                }                // 处理结束时间条件
                if (fieldName === 'actualEndDate') {
                    const startDate = params.get('field_actualEndDate_start');
                    const endDate = params.get('field_actualEndDate_end');
                    console.log(`恢复结束时间条件: 开始=${startDate}, 结束=${endDate}`);
                    addEndTimeConditionWithValues(startDate, endDate);
                    return;
                }// 处理评论天数条件
                if (fieldName === 'commentDays') {
                    const minValue = params.get('field_commentDays_min');
                    const maxValue = params.get('field_commentDays_max');
                    console.log('恢复评论天数条件:', { min: minValue, max: maxValue });
                    // 确保添加评论天数条件时，fieldNames值正确设置
                    addCommentDaysConditionWithValues(minValue, maxValue);
                    return;
                }                // 处理任务阶段条件
                if (fieldName === 'taskStage') {
                    const minValue = params.get('field_taskStage_min');
                    const maxValue = params.get('field_taskStage_max');
                    console.log('恢复任务阶段条件:', { min: minValue, max: maxValue });
                    addTaskStageConditionWithValues(minValue, maxValue);
                    return;
                }                // 处理额定工期条件
                if (fieldName === 'ratedDurationDays') {
                    const minValue = params.get('field_ratedDurationDays_min');
                    const maxValue = params.get('field_ratedDurationDays_max');
                    console.log('恢复额定工期条件:', { min: minValue, max: maxValue });
                    addRatedDurationConditionWithValues(minValue, maxValue);
                    return;
                }

                // 处理剩余工期条件
                if (fieldName === 'remainingDurationDays') {
                    const minValue = params.get('field_remainingDurationDays_min');
                    const maxValue = params.get('field_remainingDurationDays_max');
                    console.log('恢复剩余工期条件:', { min: minValue, max: maxValue });
                    addRemainingDurationConditionWithValues(minValue, maxValue);
                    return;
                }

                // 处理进度条件
                if (fieldName === 'progress') {
                    const minValue = params.get('field_progress_min');
                    const maxValue = params.get('field_progress_max');
                    console.log('恢复进度条件:', { min: minValue, max: maxValue });
                    addProgressConditionWithValues(minValue, maxValue);
                    return;
                }// 处理其他条件
                const fieldValue = params.get('field_' + fieldName);
                if (fieldValue) {
                    console.log(`恢复普通条件: 字段=${fieldName}, 值=${fieldValue}`);
                    addSearchConditionWithValues(fieldName, fieldValue);
                }
            });            console.log('搜索条件恢复完成');
            
            // 检查是否需要自动执行搜索（只在从仪表板跳转到管理页面时自动执行一次）
            if (uniqueFieldNames.length > 0) {
                // 检查当前URL路径，只有在 /tasks/management 页面才自动提交
                // 如果是 /tasks/advanced-search 页面，说明已经是搜索结果页面，不需要再次提交
                const currentPath = window.location.pathname;
                console.log('当前页面路径:', currentPath);
                
                if (currentPath === '/tasks/management' || currentPath === '/tasks/management-tasks') {
                    console.log('检测到管理页面的搜索条件，准备自动执行搜索');
                    // 使用setTimeout确保DOM完全更新后再提交
                    setTimeout(() => {
                        const form = document.getElementById('advancedSearchForm');
                        if (form) {
                            console.log('自动提交搜索表单');
                            form.submit();
                        }
                    }, 100);
                } else {
                    console.log('当前在搜索结果页面，不需要自动提交表单');
                }
            }
        }        // 使用指定的值添加搜索条件
        function addSearchConditionWithValues(fieldName, fieldValue) {
            console.log(`添加搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
            const searchConditions = document.getElementById('searchConditions');
            const condition = document.createElement('div');
            condition.className = 'search-condition row mb-3';

            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';

            // 创建字段选择下拉框
            const select = document.createElement('select');
            select.className = 'form-select search-field';
            select.setAttribute('onchange', 'updateValueField(this)');            select.innerHTML = `
                <option value="">选择字段</option>
                <option value="taskName">任务名称</option>
                <option value="responsible">负责人</option>
                <option value="status">状态</option>
                <option value="risk">风险等级</option>
                <option value="type">任务类型</option>
                <option value="projectName">所属项目</option>
                <option value="customerName">客户名称</option>
                <option value="visionType">视觉类型</option>
                <option value="taskStage">任务阶段</option>
                <option value="ratedDurationDays">额定工期</option>
                <option value="remainingDurationDays">剩余工期</option>
            `;
            
            // 设置选中的字段
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === fieldName) {
                    select.options[i].selected = true;
                    break;
                }
            }
            fieldCol.appendChild(select);

            // 创建值容器
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7 value-container';
            valueCol.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';

            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-outline-danger w-100';
            deleteBtn.setAttribute('onclick', 'removeCondition(this)');
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            btnCol.appendChild(deleteBtn);

            // 组装条件元素
            condition.appendChild(fieldCol);
            condition.appendChild(valueCol);
            condition.appendChild(btnCol);
            searchConditions.appendChild(condition);

            // 先调用updateValueField来创建正确的输入控件
            updateValueField(select);
            
            // 然后设置值
            setTimeout(() => {
                const valueContainer = condition.querySelector('.value-container');
                const inputField = valueContainer.querySelector('input[name="field_' + fieldName + '"], select[name="field_' + fieldName + '"]');
                if (inputField && fieldValue) {
                    inputField.value = fieldValue;
                    console.log(`已设置字段 ${fieldName} 的值为: ${fieldValue}`);
                }
            }, 10);

            console.log(`已添加搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
        }

        // 移除条件
        function removeCondition(button) {
            const condition = button.closest('.search-condition');

            // 如果只有一个搜索条件，不删除，只重置
            if (document.querySelectorAll('.search-condition').length <= 1) {
                // 创建一个新的搜索条件来替换当前条件
                addSearchCondition();
                // 删除旧的条件
                condition.remove();
                return;
            }

            condition.remove();
            console.log('已移除搜索条件');
        }        // 带有指定值添加结束时间条件
        function addEndTimeConditionWithValues(startDate, endDate) {
            console.log(`添加结束时间条件: 开始日期=${startDate}, 结束日期=${endDate}`);
            const conditions = document.getElementById('searchConditions');
            const endTimeCondition = document.createElement('div');
            endTimeCondition.className = 'search-condition row mb-3';
            endTimeCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" name="fieldNames" disabled>
                        <option value="actualEndDate" selected>结束时间</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="actualEndDate">
                </div>
                <div class="col-10 col-sm-5 col-md-7">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">从</span>
                                <input type="date" class="form-control" name="field_actualEndDate_start">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" name="field_actualEndDate_end">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(endTimeCondition);
            
            // 设置实际的日期值
            const startInput = endTimeCondition.querySelector('input[name="field_actualEndDate_start"]');
            const endInput = endTimeCondition.querySelector('input[name="field_actualEndDate_end"]');
            if (startInput && startDate) {
                startInput.value = startDate;
            }
            if (endInput && endDate) {
                endInput.value = endDate;
            }
            
            console.log(`已添加结束时间条件: 开始日期=${startDate}, 结束日期=${endDate}`);
        }// 带有指定值添加时间条件
        function addTimeConditionWithValues(startDate, endDate) {
            console.log(`添加创建时间条件: 开始日期=${startDate}, 结束日期=${endDate}`);
            const conditions = document.getElementById('searchConditions');
            const timeCondition = document.createElement('div');
            timeCondition.className = 'search-condition row mb-3';
            
            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';
            fieldCol.innerHTML = `
                <select class="form-select search-field" name="fieldNames" disabled>
                    <option value="createdDate" selected>创建日期</option>
                </select>
                <input type="hidden" name="fieldNames" value="createdDate">
            `;
              // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7';
            valueCol.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">从</span>
                            <input type="date" class="form-control" name="field_createdDate_start">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">至</span>
                            <input type="date" class="form-control" name="field_createdDate_end">
                        </div>
                    </div>
                </div>
            `;
            
            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            btnCol.innerHTML = `
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            
            // 组装条件元素
            timeCondition.appendChild(fieldCol);
            timeCondition.appendChild(valueCol);
            timeCondition.appendChild(btnCol);
            conditions.appendChild(timeCondition);
            
            // 设置实际的日期值
            const startInput = timeCondition.querySelector('input[name="field_createdDate_start"]');
            const endInput = timeCondition.querySelector('input[name="field_createdDate_end"]');
            if (startInput && startDate) {
                startInput.value = startDate;
            }
            if (endInput && endDate) {
                endInput.value = endDate;
            }            
            console.log(`已添加时间条件: 开始日期=${startDate}, 结束日期=${endDate}`);
        }

        // 带有指定值添加评论天数条件
        function addCommentDaysConditionWithValues(minDays, maxDays) {
            console.log(`添加评论天数条件: 最小=${minDays}, 最大=${maxDays}`);
            const conditions = document.getElementById('searchConditions');
            const commentCondition = document.createElement('div');
            commentCondition.className = 'search-condition row mb-3';
              // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';
            fieldCol.innerHTML = `
                <select class="form-select search-field" disabled>
                    <option value="commentDays" selected>评论天数</option>
                </select>
                <input type="hidden" name="fieldNames" value="commentDays" class="comment-days-field">
            `;
            
            // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7';
            valueCol.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最小</span>
                            <input type="number" class="form-control" name="field_commentDays_min" min="0" placeholder="最小天数">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最大</span>
                            <input type="number" class="form-control" name="field_commentDays_max" min="0" placeholder="最大天数">
                        </div>
                    </div>
                </div>
            `;
            
            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            btnCol.innerHTML = `
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            
            // 组装条件元素
            commentCondition.appendChild(fieldCol);
            commentCondition.appendChild(valueCol);
            commentCondition.appendChild(btnCol);
            conditions.appendChild(commentCondition);
            
            // 设置实际的值
            const minInput = commentCondition.querySelector('input[name="field_commentDays_min"]');
            const maxInput = commentCondition.querySelector('input[name="field_commentDays_max"]');
            if (minInput && minDays) {
                minInput.value = minDays;
            }
            if (maxInput && maxDays) {
                maxInput.value = maxDays;
            }
            
            console.log(`已添加评论天数条件: 最小=${minDays}, 最大=${maxDays}`);
        }

        // 带有指定值添加任务阶段条件
        function addTaskStageConditionWithValues(minStage, maxStage) {
            console.log(`添加任务阶段条件: 最小=${minStage}, 最大=${maxStage}`);
            const conditions = document.getElementById('searchConditions');
            const stageCondition = document.createElement('div');
            stageCondition.className = 'search-condition row mb-3';
            
            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';
            fieldCol.innerHTML = `
                <select class="form-select search-field" disabled>
                    <option value="taskStage" selected>任务阶段</option>
                </select>
                <input type="hidden" name="fieldNames" value="taskStage">
            `;
            
            // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7';
            valueCol.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最小</span>
                            <input type="number" class="form-control" name="field_taskStage_min" min="1" max="6" placeholder="最小阶段">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最大</span>
                            <input type="number" class="form-control" name="field_taskStage_max" min="1" max="6" placeholder="最大阶段">
                        </div>
                    </div>
                </div>
            `;
            
            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            btnCol.innerHTML = `
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            
            // 组装条件元素
            stageCondition.appendChild(fieldCol);
            stageCondition.appendChild(valueCol);
            stageCondition.appendChild(btnCol);
            conditions.appendChild(stageCondition);
            
            // 设置实际的值
            const minInput = stageCondition.querySelector('input[name="field_taskStage_min"]');
            const maxInput = stageCondition.querySelector('input[name="field_taskStage_max"]');
            if (minInput && minStage) {
                minInput.value = minStage;
            }
            if (maxInput && maxStage) {
                maxInput.value = maxStage;
            }
              console.log(`已添加任务阶段条件: 最小=${minStage}, 最大=${maxStage}`);
        }

        // 带有指定值添加进度条件
        function addProgressConditionWithValues(minProgress, maxProgress) {
            console.log(`添加进度条件: 最小=${minProgress}, 最大=${maxProgress}`);
            const conditions = document.getElementById('searchConditions');
            const progressCondition = document.createElement('div');
            progressCondition.className = 'search-condition row mb-3';
            
            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';
            fieldCol.innerHTML = `
                <select class="form-select search-field" disabled>
                    <option value="progress" selected>进度</option>
                </select>
                <input type="hidden" name="fieldNames" value="progress">
            `;
            
            // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7';
            valueCol.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最小</span>
                            <input type="number" class="form-control" name="field_progress_min" min="0" max="100" placeholder="最小进度%">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最大</span>
                            <input type="number" class="form-control" name="field_progress_max" min="0" max="100" placeholder="最大进度%">
                        </div>
                    </div>
                </div>
            `;
            
            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            btnCol.innerHTML = `
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            
            // 组装条件元素
            progressCondition.appendChild(fieldCol);
            progressCondition.appendChild(valueCol);
            progressCondition.appendChild(btnCol);
            conditions.appendChild(progressCondition);
            
            // 设置实际的值
            const minInput = progressCondition.querySelector('input[name="field_progress_min"]');
            const maxInput = progressCondition.querySelector('input[name="field_progress_max"]');
            if (minInput && minProgress) {
                minInput.value = minProgress;
            }
            if (maxInput && maxProgress) {
                maxInput.value = maxProgress;
            }
            
            console.log(`已添加进度条件: 最小=${minProgress}, 最大=${maxProgress}`);
        }

        // 添加额定工期条件（带值）
        function addRatedDurationConditionWithValues(minDuration, maxDuration) {
            console.log(`添加额定工期条件: 最小=${minDuration}, 最大=${maxDuration}`);
            const conditions = document.getElementById('searchConditions');
            const durationCondition = document.createElement('div');
            durationCondition.className = 'search-condition row mb-3';
            
            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';
            fieldCol.innerHTML = `
                <select class="form-select search-field" disabled>
                    <option value="ratedDurationDays" selected>额定工期</option>
                </select>
                <input type="hidden" name="fieldNames" value="ratedDurationDays">
            `;
            
            // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7';
            valueCol.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最小值</span>
                            <input type="number" class="form-control" name="field_ratedDurationDays_min" min="0" step="0.1" placeholder="0">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最大值</span>
                            <input type="number" class="form-control" name="field_ratedDurationDays_max" min="0" step="0.1" placeholder="不限">
                        </div>
                    </div>
                </div>
            `;
            
            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            btnCol.innerHTML = `
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            
            // 组装条件元素
            durationCondition.appendChild(fieldCol);
            durationCondition.appendChild(valueCol);
            durationCondition.appendChild(btnCol);
            conditions.appendChild(durationCondition);
            
            // 设置实际的值
            const minInput = durationCondition.querySelector('input[name="field_ratedDurationDays_min"]');
            const maxInput = durationCondition.querySelector('input[name="field_ratedDurationDays_max"]');
            if (minInput && minDuration) {
                minInput.value = minDuration;
            }
            if (maxInput && maxDuration) {
                maxInput.value = maxDuration;            }            console.log(`已添加额定工期条件: 最小=${minDuration}, 最大=${maxDuration}`);
        }

        // 带有指定值添加剩余工期条件
        function addRemainingDurationConditionWithValues(minDuration, maxDuration) {
            console.log(`添加剩余工期条件: 最小=${minDuration}, 最大=${maxDuration}`);
            const conditions = document.getElementById('searchConditions');
            const durationCondition = document.createElement('div');
            durationCondition.className = 'search-condition row mb-3';
            
            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';
            fieldCol.innerHTML = `
                <select class="form-select search-field" disabled>
                    <option value="remainingDurationDays" selected>剩余工期</option>
                </select>
                <input type="hidden" name="fieldNames" value="remainingDurationDays">
            `;
            
            // 创建值输入列
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7';
            valueCol.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最小值</span>
                            <input type="number" class="form-control" name="field_remainingDurationDays_min" step="0.1" placeholder="不限">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <span class="input-group-text">最大值</span>
                            <input type="number" class="form-control" name="field_remainingDurationDays_max" step="0.1" placeholder="不限">
                        </div>
                    </div>
                </div>
            `;
            
            // 创建删除按钮列
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';
            btnCol.innerHTML = `
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            
            // 组装条件元素
            durationCondition.appendChild(fieldCol);
            durationCondition.appendChild(valueCol);
            durationCondition.appendChild(btnCol);
            conditions.appendChild(durationCondition);
            
            // 设置实际的值
            const minInput = durationCondition.querySelector('input[name="field_remainingDurationDays_min"]');
            const maxInput = durationCondition.querySelector('input[name="field_remainingDurationDays_max"]');
            if (minInput && minDuration) {
                minInput.value = minDuration;
            }
            if (maxInput && maxDuration) {
                maxInput.value = maxDuration;
            }
            console.log(`已添加剩余工期条件: 最小=${minDuration}, 最大=${maxDuration}`);
        }

        // 更新所有进行中任务的实际工期
        function updateAllInProgressTasksDuration() {
            const button = document.getElementById('updateAllInProgressTasksDurationBtn');
            const originalText = button.innerHTML;
            
            // 确认对话框
            if (!confirm('确定要更新所有进行中任务的实际工期吗？\n\n此操作将：\n1. 暂停所有进行中的任务并计算工期\n2. 将任务状态重新设置为进行中\n3. 更新实际开始时间为当前时间\n\n这可能需要一些时间，请耐心等待。')) {
                return;
            }
            
            // 显示加载状态
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>正在更新...';
            
            // 发送AJAX请求
            fetch('/tasks/update-all-in-progress-tasks-duration', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 成功消息
                    showAlert('success', '更新成功', data.message);
                    
                    // 延迟1秒后刷新页面以显示最新数据
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    // 错误消息
                    showAlert('danger', '更新失败', data.message);
                }
            })
            .catch(error => {
                console.error('更新失败:', error);
                showAlert('danger', '更新失败', '网络错误或服务器错误，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // 修复所有任务的累计工期和剩余工期字段
        function updateAllTasksDurationFields() {
            const button = document.getElementById('updateAllTasksDurationFieldsBtn');
            const originalText = button.innerHTML;
            
            // 确认对话框
            if (!confirm('确定要修复所有任务的累计工期和剩余工期字段吗？\n\n此操作将：\n1. 重新计算所有任务的累计工期\n2. 重新计算所有任务的剩余工期\n3. 将计算结果保存到数据库中\n\n这可能需要一些时间，请耐心等待。')) {
                return;
            }
            
            // 显示加载状态
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>正在更新...';
            
            // 发送AJAX请求
            fetch('/tasks/update-all-tasks-duration-fields', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 成功消息
                    showAlert('success', '更新成功', 
                        `${data.message}\n\n成功更新: ${data.successCount} 个任务\n失败: ${data.errorCount} 个任务`);
                    
                    // 延迟2秒后刷新页面以显示最新数据
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    // 错误消息
                    showAlert('danger', '更新失败', data.message);
                }
            })
            .catch(error => {
                console.error('更新失败:', error);
                showAlert('danger', '更新失败', '网络错误或服务器错误，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // 显示提示消息的通用函数
        function showAlert(type, title, message) {
            // 创建提示框
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
            alertDiv.innerHTML = `
                <strong>${title}:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
            `;
            
            // 添加到页面
            document.body.appendChild(alertDiv);
            
            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv && alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>